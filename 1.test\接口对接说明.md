# 智能家居系统接口对接说明

## 一、总体架构

该项目使用uni-app框架进行开发，通过封装的HTTP请求模块实现与后端API的通信。主要包含以下几个部分：

1. HTTP请求封装模块（request.js）：负责处理HTTP请求，包括请求拦截器和响应拦截器
2. API服务模块：按功能分类封装各个接口
3. Vue组件：在组件中调用API服务实现业务功能

## 二、目录结构

```
src/
  ├── utils/              # 工具类
  │   └── request.js      # HTTP请求封装
  ├── api/                # API服务
  │   ├── index.js        # API服务入口
  │   ├── user.js         # 用户相关接口
  │   ├── device.js       # 设备相关接口
  │   ├── room.js         # 房间相关接口
  │   └── data.js         # 数据相关接口
  └── pages/              # 页面组件
      ├── index/          # 登录页面
      ├── home/           # 首页
      └── ...             # 其他页面
```

## 三、接口对接方式

### 1. HTTP请求封装

在`src/utils/request.js`中封装了HTTP请求方法，统一处理请求和响应：

- 请求拦截器：自动添加token、设置Content-Type等
- 响应拦截器：统一处理响应状态码、错误提示等
- 导出get、post、put、delete等方法

### 2. API服务模块

按功能分类封装各个接口：

- `user.js`：用户相关接口，如登录、注册、获取用户信息等
- `device.js`：设备相关接口，如获取设备列表、控制设备等
- `room.js`：房间相关接口，如获取房间列表、添加房间等
- `data.js`：数据相关接口，如获取实时数据、历史数据等

### 3. 使用方式

在Vue组件中通过`this.$api`调用接口服务：

```javascript
// 登录
async login() {
  try {
    const result = await this.$api.user.login({
      username: this.username,
      password: this.password
    });
    
    // 处理登录成功
    if (result && result.data) {
      // 保存token
      uni.setStorageSync('token', result.data.token);
      
      // 跳转到首页
      uni.redirectTo({ url: '/pages/home/<USER>' });
    }
  } catch (error) {
    console.error('登录失败', error);
  }
}

// 获取设备列表
async getDeviceList() {
  try {
    const result = await this.$api.device.getDeviceList({
      roomId: this.roomId
    });
    
    if (result && result.data && result.data.devices) {
      this.deviceList = result.data.devices;
    }
  } catch (error) {
    console.error('获取设备列表失败', error);
  }
}
```

## 四、API接口列表

所有接口详情参见`智能家居系统接口文档.md`文件，下面列出主要接口：

### 1. 用户相关接口

| 接口名称 | 接口地址 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| 用户注册 | /api/user/register | POST | 新用户注册 |
| 用户登录 | /api/user/login | POST | 用户登录 |
| 发送验证码 | /api/user/sendVerificationCode | POST | 发送手机验证码 |
| 获取用户信息 | /api/user/info | GET | 获取当前用户信息 |
| 修改用户信息 | /api/user/updateInfo | PUT | 更新用户资料 |
| 修改密码 | /api/user/changePassword | PUT | 修改用户密码 |
| 重置密码 | /api/user/resetPassword | POST | 忘记密码时重置 |

### 2. 设备相关接口

| 接口名称 | 接口地址 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| 获取设备列表 | /api/device/list | GET | 获取设备列表 |
| 控制设备 | /api/device/control | POST | 操作设备开关等 |
| 添加设备 | /api/device/add | POST | 添加新设备 |
| 修改设备信息 | /api/device/update | PUT | 更新设备信息 |
| 删除设备 | /api/device/delete | DELETE | 删除设备 |

### 3. 房间相关接口

| 接口名称 | 接口地址 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| 获取房间列表 | /api/room/list | GET | 获取房间列表 |
| 添加房间 | /api/room/add | POST | 添加新房间 |

### 4. 数据相关接口

| 接口名称 | 接口地址 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| 获取实时数据 | /api/data/realtime | GET | 获取设备实时数据 |
| 获取历史数据 | /api/data/history | GET | 获取设备历史数据 |
| 获取能耗统计 | /api/data/energyStats | GET | 获取能耗统计 |

## 五、异常处理

1. 网络错误处理：在request.js中统一处理网络请求错误
2. 业务错误处理：根据接口返回的code判断业务是否成功，失败则显示错误信息
3. 登录失效处理：当接口返回401时，清除token并跳转到登录页

## 六、注意事项

1. 所有需要认证的接口必须在请求头中携带token
2. 接口返回格式统一为`{ code, message, data }`，code=200表示成功
3. 在组件中调用接口时要使用try-catch捕获异常
4. 可能耗时较长的接口调用要显示加载提示 