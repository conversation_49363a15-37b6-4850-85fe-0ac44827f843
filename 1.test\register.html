<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>智能家居系统注册</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            height: 100vh;
            overflow: auto;
        }
        
        .container {
            display: flex;
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            background: none;
        }
        
        /* 原型部分 */
        .prototype {
            width: 100%;
            max-width: 375px;
            height: auto;
            aspect-ratio: 375/812;
            background: linear-gradient(145deg, #f0f0f0, #fafafa);
            border-radius: 50px;
            overflow: auto;
            box-shadow: 0 20px 60px -10px rgba(30, 136, 229, 0.18);
            position: relative;
            max-height: 92vh;
        }
        
        .register-container {
            padding: 7% 5% 5%;
            display: flex;
            flex-direction: column;
            height: 100%;
            position: relative;
            overflow: auto;
            box-shadow: inset 0 0 24px rgba(30, 136, 229, 0.06);
            background: rgba(255,255,255,0.98);
            backdrop-filter: blur(2px);
        }
        
        .register-container::-webkit-scrollbar {
            width: 4px;
        }
        
        .register-container::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .register-container::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 20px;
        }
        
        .register-container::before {
            content: "";
            position: absolute;
            top: -150px;
            right: -100px;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(30, 136, 229, 0.1), rgba(30, 136, 229, 0.05));
            z-index: 0;
        }
        
        .register-container::after {
            content: "";
            position: absolute;
            bottom: -150px;
            left: -100px;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(30, 136, 229, 0.05), rgba(30, 136, 229, 0.02));
            z-index: 0;
        }
        
        .content-wrapper {
            position: relative;
            z-index: 1;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .logo-container {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .logo {
            font-size: 32px;
            font-weight: 700;
            color: #222;
            position: relative;
            display: inline-block;
        }
        
        .logo::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #1e88e5, #64b5f6);
            border-radius: 2px;
        }
        
        h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 4px;
            color: #222;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #999;
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 16px;
        }
        
        .form-section-title {
            font-size: 14px;
            font-weight: 600;
            color: #666;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .form-section-title::after {
            content: "";
            flex: 1;
            height: 1px;
            background-color: #e0e0e0;
            margin-left: 10px;
        }
        
        .input-container {
            position: relative;
            width: 100%;
            margin-bottom: 16px;
        }
        
        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            z-index: 1; /* 降低图标层级 */
            pointer-events: none; /* 确保图标不会拦截点击事件 */
        }
        
        .input-icon svg {
            width: 20px;
            height: 20px;
        }
        
        .input-field {
            width: 100%;
            padding: 16px 20px;
            font-size: 16px;
            border: none;
            border-radius: 16px;
            background-color: #fff;
            color: #333;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .input-with-icon {
            padding-left: 50px; /* 增加左内边距，确保文本不会被图标覆盖 */
        }
        
        .input-field:focus {
            outline: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(30, 136, 229, 0.2);
            transform: translateY(-2px);
        }
        
        /* 错误状态 */
        .input-field.error {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 0 0 2px rgba(244, 67, 54, 0.2);
        }
        
        /* 成功状态 */
        .input-field.success {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 0 0 2px rgba(76, 175, 80, 0.2);
        }
        
        /* 验证消息 */
        .validation-message {
            font-size: 12px;
            margin-top: 6px;
            padding-left: 10px;
            display: none;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .validation-message.error {
            display: flex;
            color: #f44336;
        }
        
        .validation-message.success {
            display: flex;
            color: #4caf50;
        }
        
        .validation-message svg {
            width: 14px;
            height: 14px;
            margin-right: 6px;
        }
        
        .input-row {
            display: flex;
            gap: 12px;
        }
        
        .input-row .input-container {
            flex: 1;
        }
        
        .verification-container {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .verification-container .input-container {
            flex: 1;
        }
        
        .captcha-container {
            padding: 10px 16px;
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
            color: #333;
            font-size: 22px;
            font-weight: 700;
            letter-spacing: 4px;
            text-align: center;
            border-radius: 16px;
            min-width: 120px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            user-select: none;
            font-family: 'Courier New', monospace;
        }
        
        .captcha-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(45deg, transparent 45%, rgba(0,0,0,0.05) 50%, transparent 55%),
                linear-gradient(-45deg, transparent 45%, rgba(0,0,0,0.05) 50%, transparent 55%);
            background-size: 6px 6px;
            z-index: 1;
        }
        
        .refresh-captcha {
            position: absolute;
            right: 6px;
            bottom: 6px;
            width: 16px;
            height: 16px;
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0.6;
            transition: all 0.3s ease;
        }
        
        .refresh-captcha:hover {
            opacity: 1;
            transform: rotate(180deg);
        }
        
        .refresh-captcha svg {
            width: 100%;
            height: 100%;
        }
        
        .verification-button {
            padding: 0 16px;
            background: linear-gradient(135deg, #64b5f6, #1e88e5);
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            white-space: nowrap;
        }
        
        .verification-button:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .verification-button:active {
            transform: scale(0.98);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .checkbox-container {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            margin-top: 8px;
        }
        
        /* 隐藏原生复选框但保持其可访问性 */
        .checkbox-input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }
        
        /* 自定义复选框外观 */
        .checkbox-label {
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .checkbox-label:before {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 2px solid #ccc;
            margin-right: 10px;
            background-color: white;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }
        
        /* 选中状态 */
        .checkbox-input:checked + .checkbox-label:before {
            background-color: #1e88e5;
            border-color: #1e88e5;
        }
        
        /* 勾选标记 */
        .checkbox-input:checked + .checkbox-label:after {
            content: '';
            position: absolute;
            left: 7px;  /* 调整位置 */
            top: 9px;   /* 调整位置 */
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
        .checkbox-label a {
            color: #1e88e5;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .checkbox-label a:hover {
            text-decoration: underline;
        }
        
        .register-button {
            background: linear-gradient(135deg, #222, #444);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 18px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            margin-bottom: 16px;
        }
        
        .register-button::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s ease;
        }
        
        .register-button:hover::after {
            left: 100%;
        }
        
        .register-button:active {
            transform: scale(0.98);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* 提示弹窗 */
        .toast-container {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            width: 320px;
            max-width: 90%;
            pointer-events: none;
        }
        
        .toast {
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 16px;
            background-color: white;
            color: #333;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            transform: translateY(-20px);
            opacity: 0;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .toast.show {
            transform: translateY(0);
            opacity: 1;
        }
        
        .toast.success {
            border-left: 4px solid #4caf50;
        }
        
        .toast.error {
            border-left: 4px solid #f44336;
        }
        
        .toast.info {
            border-left: 4px solid #2196f3;
        }
        
        .toast-icon {
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .toast-icon svg {
            width: 20px;
            height: 20px;
        }
        
        .toast.success .toast-icon {
            color: #4caf50;
        }
        
        .toast.error .toast-icon {
            color: #f44336;
        }
        
        .toast.info .toast-icon {
            color: #2196f3;
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 16px;
        }
        
        .toast-message {
            font-size: 14px;
            color: #666;
        }
        
        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background-color: rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .toast-progress-bar {
            height: 100%;
            transition: width linear;
        }
        
        .toast.success .toast-progress-bar {
            background-color: #4caf50;
        }
        
        .toast.error .toast-progress-bar {
            background-color: #f44336;
        }
        
        .toast.info .toast-progress-bar {
            background-color: #2196f3;
        }
        
        /* 登录提示 */
        .login-prompt {
            text-align: center;
            font-size: 14px;
            color: #999;
            margin: 16px 0;
        }
        
        .login-prompt a {
            color: #1e88e5;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .login-prompt a:hover {
            text-decoration: underline;
        }
        
        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 4px;
            background-color: #e0e0e0;
            border-radius: 2px;
            margin-bottom: 24px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            width: 50%;
            background: linear-gradient(90deg, #1e88e5, #64b5f6);
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        /* 删除设计说明相关样式 */
        /* 删除 .design-notes、.design-note 及相关样式 */
        
        /* 删除颜色调色板相关样式 */
        /* 删除 .color-palette、.color-box 及相关样式 */
        
        /* 删除 .primary、.secondary、.accent、.text-dark、.text-light、.background 及相关样式 */
        
        @media (max-width: 1000px) {
            .container {
                flex-direction: column;
                align-items: center;
                padding: 0;
            }
            .prototype {
                max-width: 100vw;
                max-height: 90vh;
                border-radius: 30px;
            }
            .register-container {
                padding: 10% 5%;
            }
        }
        @media (max-width: 480px) {
            .prototype {
                border-radius: 18px;
                max-height: 98vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 原型部分 -->
        <div class="prototype">
            <div class="register-container">
                <div class="content-wrapper">
                    <div class="logo-container">
                        <div class="logo">SmartHome</div>
                    </div>
                    
                    <h1>创建账号</h1>
                    <p class="subtitle">加入智能家居系统，享受智能生活</p>
                    
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    
                    <div class="form-section">
                        <div class="form-section-title">基本信息</div>
                        
                        <div class="input-row">
                            <div class="input-container">
                                <div class="input-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </div>
                                <input type="text" id="firstName" class="input-field input-with-icon" placeholder="姓氏">
                            </div>
                            
                            <div class="input-container">
                                <input type="text" id="lastName" class="input-field" placeholder="名字">
                            </div>
                        </div>
                        
                        <div class="input-container">
                            <div class="input-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                    <polyline points="22,6 12,13 2,6"></polyline>
                                </svg>
                            </div>
                            <input type="email" id="email" class="input-field input-with-icon" placeholder="电子邮箱">
                            <div class="validation-message" id="emailValidation"></div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <div class="form-section-title">验证码确认</div>
                        
                        <div class="verification-container">
                            <div class="input-container">
                                <div class="input-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                                        <path d="M2 17l10 5 10-5"></path>
                                        <path d="M2 12l10 5 10-5"></path>
                                    </svg>
                                </div>
                                <input type="text" id="verificationCode" class="input-field input-with-icon" placeholder="请输入右侧验证码">
                                <div class="validation-message" id="codeValidation"></div>
                            </div>
                            <div class="captcha-container" id="captchaDisplay">
                                A7B3X5
                                <button class="refresh-captcha" id="refreshCaptcha">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M23 4v6h-6"></path>
                                        <path d="M1 20v-6h6"></path>
                                        <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                                        <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <div class="form-section-title">设置密码</div>
                        
                        <div class="input-container">
                            <div class="input-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                </svg>
                            </div>
                            <input type="password" id="password" class="input-field input-with-icon" placeholder="密码（至少8位，包含字母和数字）">
                            <div class="validation-message" id="passwordValidation"></div>
                        </div>
                        
                        <div class="input-container">
                            <div class="input-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                </svg>
                            </div>
                            <input type="password" id="confirmPassword" class="input-field input-with-icon" placeholder="确认密码">
                            <div class="validation-message" id="confirmPasswordValidation"></div>
                        </div>
                    </div>
                    
                    <div class="checkbox-container">
                        <input type="checkbox" id="termsCheckbox" class="checkbox-input">
                        <label for="termsCheckbox" class="checkbox-label">我已阅读并同意 <a href="agreement.html?source=terms" id="termsLink">用户协议</a> 和 <a href="agreement.html?source=privacy" id="privacyLink">隐私政策</a></label>
                    </div>
                    
                    <button class="register-button" id="registerButton">注 册</button>
                    
                    <div class="login-prompt">
                        已有账号？<a href="index.html">立即登录</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 删除设计说明HTML部分 -->
    </div>

    <!-- 提示弹窗容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        // 添加一个简单的调试函数
        function debug(message) {
            console.log(`[DEBUG] ${message}`);
        }
        
        // 在DOM完全加载后执行所有初始化
        document.addEventListener('DOMContentLoaded', function() {
            debug('页面加载完成，初始化应用...');
            
            // 初始化Toast系统
            setTimeout(() => {
                showToast('info', '欢迎', '请填写表单完成注册');
            }, 500);
            
            // 生成随机验证码
            function generateCaptcha() {
                const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                let captcha = '';
                for (let i = 0; i < 6; i++) {
                    captcha += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return captcha;
            }
            
            // 显示验证码
            const captchaDisplay = document.getElementById('captchaDisplay');
            if (!captchaDisplay) {
                console.error('找不到验证码显示元素!');
                return;
            }
            
            let currentCaptcha = generateCaptcha();
            captchaDisplay.textContent = currentCaptcha;
            
            // 刷新验证码
            const refreshCaptchaBtn = document.getElementById('refreshCaptcha');
            if (refreshCaptchaBtn) {
                refreshCaptchaBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    currentCaptcha = generateCaptcha();
                    captchaDisplay.textContent = currentCaptcha;
                    
                    // 添加刷新按钮的动画
                    this.style.transform = 'rotate(180deg)';
                    setTimeout(() => {
                        this.style.transform = 'rotate(0deg)';
                    }, 300);
                    
                    debug('验证码已刷新: ' + currentCaptcha);
                });
            }
            
            // 检查URL参数，如果从协议页面返回且已同意，则自动勾选
            const urlParams = new URLSearchParams(window.location.search);
            const termsCheckbox = document.getElementById('termsCheckbox');
            
            if (termsCheckbox && urlParams.get('agreed') === 'true') {
                termsCheckbox.checked = true;
                debug('从协议页面返回，自动勾选同意框');
            }
            
            // 验证函数
            function validateEmail(email) {
                const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(email.toLowerCase());
            }
            
            function validatePassword(password) {
                // 至少8位，至少包含一个字母和一个数字
                const re = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/;
                return re.test(password);
            }
            
            function showValidationMessage(inputId, isValid, message) {
                const input = document.getElementById(inputId);
                const validationMsg = document.getElementById(inputId + 'Validation');
                
                if (!input || !validationMsg) return;
                
                if (isValid === null) {
                    // 清除验证状态
                    input.classList.remove('error', 'success');
                    validationMsg.classList.remove('error', 'success');
                    validationMsg.innerHTML = '';
                    validationMsg.style.display = 'none';
                    return;
                }
                
                // 移除之前的状态
                input.classList.remove('error', 'success');
                validationMsg.classList.remove('error', 'success');
                
                // 添加新状态
                const status = isValid ? 'success' : 'error';
                input.classList.add(status);
                validationMsg.classList.add(status);
                
                // 设置图标和消息
                const icon = isValid ? 
                    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>' :
                    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>';
                
                validationMsg.innerHTML = icon + message;
                validationMsg.style.display = 'flex';
            }
            
            // 实时验证
            const emailInput = document.getElementById('email');
            if (emailInput) {
                emailInput.addEventListener('input', function() {
                    if (this.value === '') {
                        showValidationMessage('email', null);
                        return;
                    }
                    
                    const isValid = validateEmail(this.value);
                    showValidationMessage('email', isValid, isValid ? '邮箱格式正确' : '请输入有效的电子邮箱地址');
                });
            }
            
            const verificationCodeInput = document.getElementById('verificationCode');
            if (verificationCodeInput) {
                verificationCodeInput.addEventListener('input', function() {
                    if (this.value === '') {
                        showValidationMessage('verificationCode', null);
                        return;
                    }
                    
                    const isValid = this.value.toUpperCase() === currentCaptcha;
                    showValidationMessage('verificationCode', isValid, isValid ? '验证码正确' : '验证码不匹配');
                });
            }
            
            const passwordInput = document.getElementById('password');
            if (passwordInput) {
                passwordInput.addEventListener('input', function() {
                    if (this.value === '') {
                        showValidationMessage('password', null);
                        return;
                    }
                    
                    const isValid = validatePassword(this.value);
                    showValidationMessage('password', isValid, isValid ? '密码格式正确' : '密码至少需要8位，包含字母和数字');
                    
                    // 如果确认密码不为空，则同时验证两个密码是否一致
                    const confirmPassword = document.getElementById('confirmPassword');
                    if (confirmPassword && confirmPassword.value !== '') {
                        const isMatch = this.value === confirmPassword.value;
                        showValidationMessage('confirmPassword', isMatch, isMatch ? '两次密码输入一致' : '两次输入的密码不一致');
                    }
                });
            }
            
            const confirmPasswordInput = document.getElementById('confirmPassword');
            if (confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', function() {
                    if (this.value === '') {
                        showValidationMessage('confirmPassword', null);
                        return;
                    }
                    
                    const password = document.getElementById('password');
                    if (password) {
                        const isValid = this.value === password.value;
                        showValidationMessage('confirmPassword', isValid, isValid ? '两次密码输入一致' : '两次输入的密码不一致');
                    }
                });
            }
            
            // 输入框焦点效果
            const inputs = document.querySelectorAll('.input-field');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    if (!this.classList.contains('error')) {
                        this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(30, 136, 229, 0.2)';
                    }
                    this.style.transform = 'translateY(-2px)';
                    
                    // 确保图标也跟随输入框移动
                    const iconContainer = this.previousElementSibling;
                    if (iconContainer && iconContainer.classList.contains('input-icon')) {
                        iconContainer.style.transform = 'translateY(calc(-50% - 2px))';
                    }
                });
                
                input.addEventListener('blur', function() {
                    if (!this.classList.contains('error') && !this.classList.contains('success')) {
                        this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.05)';
                    }
                    this.style.transform = 'translateY(0)';
                    
                    // 恢复图标位置
                    const iconContainer = this.previousElementSibling;
                    if (iconContainer && iconContainer.classList.contains('input-icon')) {
                        iconContainer.style.transform = 'translateY(-50%)';
                    }
                });
            });
            
            // 添加品牌标识动画效果
            const logo = document.querySelector('.logo');
            if (logo) {
                setInterval(() => {
                    logo.style.animation = 'pulse 1.5s ease-in-out';
                    setTimeout(() => {
                        logo.style.animation = 'none';
                    }, 1500);
                }, 5000);
            }
            
            // 注册按钮点击事件 - 最重要的部分
            const registerButton = document.getElementById('registerButton');
            debug('找到注册按钮: ' + (registerButton ? '是' : '否'));
            
            if (registerButton) {
                registerButton.addEventListener('click', function() {
                    debug('注册按钮被点击');
                    
                    // 获取表单数据
                    const firstName = document.getElementById('firstName')?.value || '';
                    const lastName = document.getElementById('lastName')?.value || '';
                    const email = document.getElementById('email')?.value || '';
                    const verificationCode = document.getElementById('verificationCode')?.value || '';
                    const password = document.getElementById('password')?.value || '';
                    const confirmPassword = document.getElementById('confirmPassword')?.value || '';
                    const termsChecked = document.getElementById('termsCheckbox')?.checked || false;
                    
                    debug(`表单数据: 姓=${firstName}, 名=${lastName}, 邮箱=${email}, 验证码已填=${verificationCode ? '是' : '否'}, 条款已勾选=${termsChecked}`);
                    
                    // 验证所有字段
                    let hasError = false;
                    
                    // 检查必填字段
                    if (!firstName) {
                        hasError = true;
                        showToast('error', '输入错误', '请输入姓氏');
                        debug('验证失败: 姓氏为空');
                    } else if (!lastName) {
                        hasError = true;
                        showToast('error', '输入错误', '请输入名字');
                        debug('验证失败: 名字为空');
                    } else if (!email) {
                        hasError = true;
                        showToast('error', '输入错误', '请输入电子邮箱');
                        debug('验证失败: 邮箱为空');
                    } else if (!validateEmail(email)) {
                        hasError = true;
                        showToast('error', '格式错误', '请输入有效的电子邮箱地址');
                        debug('验证失败: 邮箱格式无效');
                    } else if (!verificationCode) {
                        hasError = true;
                        showToast('error', '输入错误', '请输入验证码');
                        debug('验证失败: 验证码为空');
                    } else if (verificationCode.toUpperCase() !== currentCaptcha) {
                        hasError = true;
                        showToast('error', '验证失败', '验证码输入错误，请重新输入');
                        debug('验证失败: 验证码不匹配');
                    } else if (!password) {
                        hasError = true;
                        showToast('error', '输入错误', '请设置密码');
                        debug('验证失败: 密码为空');
                    } else if (!validatePassword(password)) {
                        hasError = true;
                        showToast('error', '密码不符合要求', '密码至少需要8位，包含字母和数字');
                        debug('验证失败: 密码格式无效');
                    } else if (!confirmPassword) {
                        hasError = true;
                        showToast('error', '输入错误', '请确认密码');
                        debug('验证失败: 确认密码为空');
                    } else if (password !== confirmPassword) {
                        hasError = true;
                        showToast('error', '密码不匹配', '两次输入的密码不一致');
                        debug('验证失败: 密码不匹配');
                    } else if (!termsChecked) {
                        hasError = true;
                        showToast('error', '条款确认', '请阅读并同意用户协议和隐私政策');
                        debug('验证失败: 未同意条款');
                    }
                    
                    if (hasError) {
                        debug('表单验证失败，停止提交');
                        return;
                    }
                    
                    // 验证通过，模拟注册成功
                    debug('表单验证通过，准备跳转');
                    this.classList.add('animate-register');
                    showToast('success', '注册成功', '您的账号已创建，即将跳转到登录页面');
                    
                    // 延迟跳转到登录页面
                    setTimeout(() => {
                        debug('跳转到登录页面: index.html');
                        window.location.href = 'index.html';
                    }, 2000);
                });
                
                debug('注册按钮事件监听器添加成功');
            } else {
                console.error('注册按钮元素未找到，无法添加点击事件!');
            }
        });
        
        // Toast提示系统
        function showToast(type, title, message, duration = 3000) {
            debug(`显示Toast: 类型=${type}, 标题=${title}, 消息=${message}`);
            
            const toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                console.error('找不到Toast容器!');
                return;
            }
            
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            
            // 根据类型设置图标
            let icon = '';
            if (type === 'success') {
                icon = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>';
            } else if (type === 'error') {
                icon = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>';
            } else if (type === 'info') {
                icon = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>';
            }
            
            // 设置Toast内容
            toast.innerHTML = `
                <div class="toast-icon">${icon}</div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <div class="toast-progress">
                    <div class="toast-progress-bar"></div>
                </div>
            `;
            
            // 添加到容器
            toastContainer.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.classList.add('show');
                
                // 设置进度条动画
                const progressBar = toast.querySelector('.toast-progress-bar');
                progressBar.style.width = '100%';
                progressBar.style.transitionDuration = `${duration}ms`;
                
                // 设置定时器自动移除
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        toastContainer.removeChild(toast);
                    }, 300);
                }, duration);
                
                // 开始进度条动画
                setTimeout(() => {
                    progressBar.style.width = '0%';
                }, 10);
            }, 10);
        }
    </script>
</body>
</html> 