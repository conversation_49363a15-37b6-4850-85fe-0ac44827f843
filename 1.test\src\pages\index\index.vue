<template>
  <view class="container">
    <!-- 原型部分 -->
    <view class="prototype">
      <view class="login-container">
        <view class="content-wrapper">
          <view class="logo-container">
            <view class="logo">SmartHome</view>
          </view>
          
          <text class="h1">欢迎</text>
          <text class="subtitle">登录智能家居系统</text>
          
          <view class="input-container">
            <view class="input-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
              </svg>
            </view>
            <input type="number" id="phone" v-model="phone" class="input-field input-with-icon" placeholder="请输入手机号码" />
          </view>
          
          <view class="input-container">
            <view class="input-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
              </svg>
            </view>
            <input :type="passwordVisible ? 'text' : 'password'" id="password" v-model="password" class="input-field input-with-icon" :class="{'password-field': !passwordVisible}" placeholder="• • • • • •" />
            <button class="toggle-password" @click="togglePassword">
              <svg v-if="!passwordVisible" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
            </button>
          </view>
          
          <view class="forgot-password">
            <text @click="forgotPassword">忘记密码？</text>
          </view>
          
          <button class="login-button" @click="login">登 录</button>
          
          <view class="signup-prompt">
            没有账号？<text @click="goToRegister">立即注册</text>
          </view>
          
          <view class="divider">
            <text>其他登录方式</text>
          </view>
          
          <view class="alt-login">
            <button class="alt-login-button" @click="smsLogin">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#1e88e5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                <line x1="9" y1="10" x2="15" y2="10"></line>
                <line x1="12" y1="7" x2="12" y2="13"></line>
              </svg>
              <text>验证码登录</text>
            </button>
            <button class="alt-login-button" @click="emailLogin">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#1e88e5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
              <text>邮箱登录</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      phone: '',
      password: '',
      passwordVisible: false
    }
  },
  onLoad() {
    // 隐藏导航栏
    uni.hideNavigationBarTitle();
  },
  methods: {
    togglePassword() {
      this.passwordVisible = !this.passwordVisible;
    },
    login() {
      if (this.phone && this.password) {
        // 使用switchTab方法替代navigateTo，因为home页面是tabBar页面
        uni.switchTab({
          url: '/pages/home/<USER>'
        });
      } else {
        uni.showToast({
          title: '请输入手机号码和密码',
          icon: 'none'
        });
      }
    },
    forgotPassword() {
      uni.showToast({
        title: '忘记密码功能将在后续版本中推出',
        icon: 'none'
      });
    },
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      });
    },
    smsLogin() {
      uni.showToast({
        title: '验证码登录功能将在后续版本中推出',
        icon: 'none'
      });
    },
    emailLogin() {
      uni.showToast({
        title: '邮箱登录功能将在后续版本中推出',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.container {
  display: flex;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  background: linear-gradient(120deg, #e0f7fa 0%, #f5f5f5 100%);
}

/* 主体内容 */
.prototype {
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.login-container {
  padding: 10% 5%;
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  overflow: auto;
  background: rgba(255,255,255,0.98);
}

/* Logo部分 */
.logo-container {
  margin-top: 10vh;
  margin-bottom: 10vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  font-size: 32px;
  font-weight: 700;
  color: #333;
  position: relative;
}

.logo::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #1976d2;
  border-radius: 2px;
}

/* 标题文字 */
.h1 {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  color: #888;
  margin-bottom: 30px;
}

/* 输入框 */
.input-container {
  position: relative;
  margin-bottom: 20px;
  width: 100%;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #999;
  z-index: 1;
}

.input-field {
  width: 100%;
  height: 50px;
  padding: 0 15px 0 45px;
  border-radius: 10px;
  border: 1px solid #e0e0e0;
  font-size: 16px;
  background-color: #f9f9f9;
}

.input-field:focus {
  border-color: #1976d2;
  outline: none;
  background-color: #fff;
}

.password-field {
  letter-spacing: 2px;
  font-size: 16px;
}

.toggle-password {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: #999;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 忘记密码 */
.forgot-password {
  text-align: right;
  margin-bottom: 25px;
}

.forgot-password text {
  color: #1976d2;
  font-size: 14px;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 50px;
  border-radius: 10px;
  background-color: #1976d2;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border: none;
  margin-bottom: 20px;
  text-align: center;
  line-height: 50px;
}

/* 注册提示 */
.signup-prompt {
  text-align: center;
  margin-bottom: 30px;
  color: #666;
  font-size: 14px;
}

.signup-prompt text {
  color: #1976d2;
  margin-left: 5px;
}

/* 分隔线 */
.divider {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid #e0e0e0;
}

.divider text {
  padding: 0 10px;
  color: #999;
  font-size: 14px;
}

/* 替代登录方式 */
.alt-login {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.alt-login-button {
  flex: 1;
  height: 50px;
  border-radius: 10px;
  border: 1px solid #e0e0e0;
  background-color: white;
  margin: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 5px;
}

.alt-login-button svg {
  width: 20px;
  height: 20px;
}

.alt-login-button text {
  font-size: 12px;
  color: #666;
}

/* 响应式调整 */
@media screen and (min-width: 500px) {
  .prototype {
    width: 100%;
    max-width: 375px;
    height: auto;
    aspect-ratio: 375/812;
    border-radius: 30px;
    box-shadow: 0 20px 60px -10px rgba(30, 136, 229, 0.18);
    max-height: 92vh;
  }
  
  .login-container {
    height: 100%;
  }
}
</style>
