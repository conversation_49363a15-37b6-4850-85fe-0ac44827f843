<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>智能家居控制系统 - 通知中心</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="key.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: flex-start;
            background: none;
        }
        
        /* 主页面设计 */
        .notification-container {
            width: 100%;
            max-width: 100%;
            height: 100vh;
            background: linear-gradient(145deg, #f0f0f0, #fafafa);
            border-radius: 0;
            overflow: hidden;
            box-shadow: 0 20px 60px -10px rgba(30, 136, 229, 0.18);
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        /* 顶部状态栏 */
        .status-bar {
            display: flex;
            justify-content: space-between;
            padding: 16px 20px 10px;
            align-items: center;
            position: relative;
            z-index: 10;
            background-color: #f0f0f0;
        }
        
        .title-section {
            display: flex;
            flex-direction: column;
        }
        
        .page-title {
            font-size: 22px;
            font-weight: 700;
            color: #222;
        }
        
        .subtitle {
            font-size: 12px;
            color: #999;
        }
        
        .back-button {
            width: 40px;
            height: 40px;
            background-color: #222;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            transform: scale(1.05);
        }
        
        .back-button svg {
            width: 20px;
            height: 20px;
            color: white;
        }
        
        /* 通知设置栏 */
        .settings-bar {
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .settings-actions {
            display: flex;
            gap: 8px;
        }
        
        .period-selector {
            display: flex;
            align-items: center;
        }
        
        .period-label {
            font-size: 14px;
            color: #666;
            margin-right: 10px;
        }
        
        .period-dropdown {
            padding: 6px 12px;
            border-radius: 8px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            font-size: 14px;
            color: #333;
            outline: none;
            cursor: pointer;
        }
        
        .clear-button {
            padding: 6px 12px;
            border-radius: 8px;
            background-color: #f44336;
            color: white;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .clear-button:hover {
            background-color: #e53935;
        }
        
        /* 通知列表 */
        .notification-list {
            flex: 1;
            overflow-y: auto;
            padding: 16px 20px;
            background-color: #f7f7f7;
        }
        
        .notification-list::-webkit-scrollbar {
            width: 4px;
        }
        
        .notification-list::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .notification-list::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 20px;
        }
        
        .notification-item {
            background-color: white;
            border-radius: 16px;
            margin-bottom: 16px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
            animation: slideIn 0.3s ease-out forwards;
        }
        
        .notification-item.normal {
            border-left: 4px solid #3498db;
        }
        
        .notification-item.changed {
            border-left: 4px solid #e67e22;
        }
        
        .notification-item.error {
            border-left: 4px solid #e74c3c;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .time-stamp {
            font-size: 12px;
            color: #999;
        }
        
        .notification-badge {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
            text-align: center;
        }
        
        .notification-badge.normal {
            background-color: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }
        
        .notification-badge.changed {
            background-color: rgba(230, 126, 34, 0.1);
            color: #e67e22;
        }
        
        .notification-badge.error {
            background-color: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }
        
        .notification-content {
            font-size: 14px;
            line-height: 1.5;
            color: #333;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 40px 0;
        }
        
        .empty-icon {
            width: 60px;
            height: 60px;
            margin-bottom: 16px;
            color: #bdc3c7;
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 600;
            color: #7f8c8d;
            margin-bottom: 8px;
        }
        
        .empty-subtitle {
            font-size: 14px;
            color: #95a5a6;
            text-align: center;
            max-width: 80%;
        }
        
        /* 底部导航栏 */
        .nav-bar, .bottom-nav {
            display: flex;
            justify-content: space-around;
            padding: 12px 0;
            background-color: white;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .nav-item.active {
            color: #1976D2;
        }
        
        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            color: #999;
        }
        
        .nav-label, .nav-text {
            font-size: 12px;
            color: #999;
        }
        
        .nav-item.active .nav-icon {
            color: #1976D2;
        }
        
        .nav-item.active .nav-label, 
        .nav-item.active .nav-text {
            color: #1976D2;
            font-weight: 500;
        }
        
        /* 通知轻提示 */
        .toast-container {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 9999;
            pointer-events: none;
        }
        
        .toast-message {
            padding: 10px 20px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 20px;
            margin-bottom: 10px;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .toast-message.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .toast-message.success {
            background-color: rgba(46, 204, 113, 0.95);
        }
        
        .toast-message.error {
            background-color: rgba(231, 76, 60, 0.95);
        }
        
        .toast-message.info {
            background-color: rgba(52, 152, 219, 0.95);
        }
        
        .toast-icon {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="notification-container">
            <!-- 顶部状态栏 -->
            <div class="status-bar">
                <div class="title-section">
                    <div class="page-title">通知中心</div>
                    <div class="subtitle">设备实时数据通知</div>
                </div>
                <div class="back-button" onclick="goBack()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: white;">
                        <path d="M19 12H5"></path>
                        <path d="M12 19l-7-7 7-7"></path>
                    </svg>
                </div>
            </div>
            
            <!-- 通知设置栏 -->
            <div class="settings-bar">
                <div class="period-selector">
                    <span class="period-label">通知周期:</span>
                    <select id="periodSelect" class="period-dropdown" onchange="changePeriod()">
                        <option value="5000">5秒</option>
                        <option value="10000">10秒</option>
                        <option value="30000">30秒</option>
                        <option value="60000">1分钟</option>
                    </select>
                </div>
                <div class="settings-actions">
                    <button class="clear-button" onclick="clearNotifications()">清空通知</button>
                </div>
            </div>
            
            <!-- 通知列表 -->
            <div id="notificationList" class="notification-list"></div>
            
            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <a href="index.html" class="nav-item">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItaG9tZSI+PHBhdGggZD0iTTMgOWw5LTcgOSA3djExYTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiI+PC9wYXRoPjxwb2x5bGluZSBwb2ludHM9IjkgMjIgOSAxMiAxNSAxMiAxNSAyMiI+PC9wb2x5bGluZT48L3N2Zz4=" class="nav-icon" />
                    <span class="nav-text">首页</span>
                </a>
                <a href="history.html" class="nav-item">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItcmVwZWF0Ij48cG9seWxpbmUgcG9pbnRzPSIxNyAxIDIxIDUgMTcgOSI+PC9wb2x5bGluZT48cGF0aCBkPSJNMyAxMVY5YTQgNCAwIDAgMSA0LTRoMTQiPjwvcGF0aD48cG9seWxpbmUgcG9pbnRzPSI3IDIzIDMgMTkgNyAxNSI+PC9wb2x5bGluZT48cGF0aCBkPSJNMjEgMTN2MmE0IDQgMCAwIDEtNCA0SDMiPjwvcGF0aD48L3N2Zz4=" class="nav-icon" />
                    <span class="nav-text">历史</span>
                </a>
                <a href="notification.html" class="nav-item active">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItYmVsbCI+PHBhdGggZD0iTTE4IDhBNiA2IDAgMCAwIDYgOGMwIDctMyA5LTMgOWgxOHMtMy0yLTMtOSI+PC9wYXRoPjxwYXRoIGQ9Ik0xMy43MyAyMWEyIDIgMCAwIDEtMy40NiAwIj48L3BhdGg+PC9zdmc+" class="nav-icon" />
                    <span class="nav-text">通知</span>
                </a>
                <a href="my.html" class="nav-item">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItdXNlciI+PHBhdGggZD0iTTIwIDIxdi0yYTQgNCAwIDAgMC00LTRIOGE0IDQgMCAwIDAtNCA0djIiPjwvcGF0aD48Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiPjwvY2lyY2xlPjwvc3ZnPg==" class="nav-icon" />
                    <span class="nav-text">我的</span>
                </a>
            </div>
            
            <style>
                .bottom-nav {
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                    background-color: #ffffff;
                    box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.05);
                    position: relative;
                    padding: 8px 0;
                    border-top: 1px solid #f0f0f0;
                }
                
                .nav-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    flex: 1;
                    padding: 8px 0;
                    text-decoration: none;
                    color: #999;
                    transition: all 0.3s;
                }
                
                .nav-item.active {
                    color: #1976D2;
                }
                
                .nav-icon {
                    width: 24px;
                    height: 24px;
                    margin-bottom: 4px;
                }
                
                .nav-item.active .nav-icon {
                    color: #1976D2;
                }
                
                .nav-text {
                    font-size: 12px;
                    font-weight: 500;
                }
            </style>
        </div>
    </div>
    
    <script>
        // 定义全局变量
        let notifications = [];
        let refreshPeriod = 5000; // 默认5秒刷新一次
        let timer = null;
        let lastData = null;
        const MAX_NOTIFICATIONS = 100;
        
        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', () => {
            // 从 localStorage 获取保存的通知
            loadNotifications();
            
            // 从 localStorage 获取保存的周期设置
            const savedPeriod = localStorage.getItem('notificationPeriod');
            if (savedPeriod) {
                refreshPeriod = parseInt(savedPeriod);
                document.getElementById('periodSelect').value = refreshPeriod;
            }
            
            // 初始化通知列表
            renderNotifications();
            
            // 开始定期获取数据
            startFetchingData();
            
            // 添加初始欢迎通知
            if (notifications.length === 0) {
                setTimeout(() => {
                    addNotification('欢迎使用通知中心！系统将每隔' + (refreshPeriod/1000) + '秒自动从OneNET云平台获取最新数据。', 'normal');
                }, 1000);
            }
            
            // 添加刷新按钮
            addRefreshButton();
        });
        
        // 添加刷新按钮
        function addRefreshButton() {
            const section = document.querySelector('.settings-bar');
            const refreshButton = document.createElement('button');
            refreshButton.className = 'refresh-button';
            refreshButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 4px;">
                    <path d="M23 4v6h-6"></path>
                    <path d="M1 20v-6h6"></path>
                    <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                    <path d="M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                </svg>
                刷新
            `;
                            refreshButton.addEventListener('click', manualRefresh);
            
            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                .refresh-button {
                    padding: 6px 12px;
                    border-radius: 8px;
                    background-color: #2196f3;
                    color: white;
                    border: none;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: background-color 0.3s;
                    display: flex;
                    align-items: center;
                }
                
                .refresh-button:hover {
                    background-color: #1976d2;
                }
            `;
            document.head.appendChild(style);
            
            // 添加到设置栏
            const actionsDiv = section.querySelector('.settings-actions');
            actionsDiv.insertBefore(refreshButton, actionsDiv.firstChild);
        }
        

        
        // 加载保存的通知
        function loadNotifications() {
            const savedNotifications = localStorage.getItem('notifications');
            if (savedNotifications) {
                try {
                    notifications = JSON.parse(savedNotifications);
                } catch (e) {
                    console.error('解析保存的通知失败:', e);
                    notifications = [];
                }
            }
        }
        
        // 保存通知到本地存储
        function saveNotifications() {
            localStorage.setItem('notifications', JSON.stringify(notifications));
        }
        
        // 渲染通知列表
        function renderNotifications() {
            const notificationList = document.getElementById('notificationList');
            notificationList.innerHTML = '';
            
            if (notifications.length === 0) {
                notificationList.innerHTML = `
                    <div class="empty-state">
                        <svg class="empty-icon" xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                            <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                        </svg>
                        <div class="empty-title">暂无通知</div>
                        <div class="empty-subtitle">设备数据将每隔 ${refreshPeriod/1000} 秒更新一次</div>
                    </div>
                `;
                return;
            }
            
            // 按时间降序排列
            const sortedNotifications = [...notifications].sort((a, b) => b.id - a.id);
            
            sortedNotifications.forEach(notification => {
                const notificationItem = document.createElement('div');
                notificationItem.className = `notification-item ${notification.type}`;
                notificationItem.innerHTML = `
                    <div class="notification-header">
                        <span class="time-stamp">${notification.time}</span>
                        <span class="notification-badge ${notification.type}">${getTypeText(notification.type)}</span>
                    </div>
                    <div class="notification-content">${notification.content}</div>
                `;
                notificationList.appendChild(notificationItem);
            });
        }
        
                 // 获取类型文字
         function getTypeText(type) {
             switch(type) {
                 case 'normal': return '普通';
                 case 'changed': return '数据变化';
                 case 'error': return '异常';
                 default: return '信息';
             }
         }
         
         // 增强版数据摘要格式化 - 更美观的布局
         function formatEnhancedDataSummary(dataItems, title) {
             const timestamp = new Date().toLocaleTimeString();
             
             // 根据数据类型判断标题颜色
             let headerClass = 'normal-header';
             if (dataItems.some(item => item.isWarning)) {
                 headerClass = 'warning-header';
             }
             
             // 创建现代化容器
             let html = `
                 <div class="data-card">
                     <div class="data-header ${headerClass}">
                         <div class="data-title">${title}</div>
                         <div class="data-timestamp">${timestamp}</div>
                     </div>
                     <div class="data-body">
             `;
             
             // 根据数据类型分组
             const sensors = dataItems.filter(item => item.name === 'PH值' || item.name === '水位');
             const pumps = dataItems.filter(item => item.name.includes('捕收剂') || item.name.includes('起泡剂') || 
                                                  item.name.includes('活化剂') || item.name.includes('抑制剂') || 
                                                  item.name.includes('调整剂'));
             
             // 添加传感器数据部分
             if (sensors.length > 0) {
                 html += `
                     <div class="data-section">
                         <div class="section-title">传感器数据</div>
                         <div class="sensor-grid">
                 `;
                 
                 sensors.forEach(item => {
                     const isPhValue = item.name === 'PH值';
                     const sensorIcon = isPhValue ? '🧪' : '💧';
                     const sensorClass = isPhValue ? 'ph-sensor' : 'water-sensor';
                     
                     // 添加值范围类
                     let valueRangeClass = '';
                     if (isPhValue) {
                         const value = item.isNew ? parseFloat(item.value) : parseFloat(item.newValue);
                         if (value < 6.5) valueRangeClass = 'value-low';
                         else if (value > 8.5) valueRangeClass = 'value-high';
                         else valueRangeClass = 'value-normal';
                     } else if (item.name === '水位') {
                         const value = item.isNew ? parseFloat(item.value) : parseFloat(item.newValue);
                         if (value < 20) valueRangeClass = 'value-low';
                         else if (value > 80) valueRangeClass = 'value-high';
                         else valueRangeClass = 'value-normal';
                     }
                     
                     let valueDisplay = '';
                     let trendIcon = '';
                     
                     if (item.isNew) {
                         valueDisplay = `<span class="value-current ${valueRangeClass}">${item.value}</span>`;
                     } else if (item.isChanged) {
                         const oldValue = parseFloat(item.oldValue);
                         const newValue = parseFloat(item.newValue);
                         const trend = newValue > oldValue ? 'up' : (newValue < oldValue ? 'down' : 'stable');
                         
                         trendIcon = trend === 'up' ? 
                             '<span class="trend-up">↑</span>' : 
                             (trend === 'down' ? '<span class="trend-down">↓</span>' : '');
                             
                         valueDisplay = `
                             <div class="value-change">
                                 <span class="value-old">${item.oldValue}</span>
                                 <span class="arrow">→</span>
                                 <span class="value-new ${valueRangeClass}">${item.newValue}</span>
                                 ${trendIcon}
                             </div>
                         `;
                     }
                     
                     // 添加警告标记并增强显示效果
                     const warningBadge = item.isWarning ? 
                         `<span class="warning-badge pulse" title="${item.warningMessage}">⚠️</span>` : '';
                     
                     html += `
                         <div class="sensor-item ${sensorClass} ${item.isWarning ? 'warning' : ''}">
                             <div class="sensor-icon">${sensorIcon}</div>
                             <div class="sensor-details">
                                 <div class="sensor-name">${item.name} ${warningBadge}</div>
                                 <div class="sensor-value">${valueDisplay}</div>
                             </div>
                         </div>
                     `;
                 });
                 
                 html += `
                         </div>
                     </div>
                 `;
             }
             
             // 添加水泵数据部分
             if (pumps.length > 0) {
                 html += `
                     <div class="data-section">
                         <div class="section-title">水泵状态</div>
                         <div class="pump-grid">
                 `;
                 
                 pumps.forEach(item => {
                     let pumpStatus = '';
                     let statusClass = '';
                     let statusIcon = '';
                     let animationClass = '';
                     
                     if (item.isNew) {
                         const isActive = item.value === 'true';
                         pumpStatus = isActive ? '运行中' : '已停止';
                         statusClass = isActive ? 'active' : 'inactive';
                         statusIcon = isActive ? 
                             '<div class="status-icon running"></div>' : 
                             '<div class="status-icon stopped"></div>';
                         animationClass = isActive ? 'pump-active' : '';
                     } else if (item.isChanged) {
                         const wasActive = item.oldValue === 'true';
                         const isActive = item.newValue === 'true';
                         
                         if (wasActive && !isActive) {
                             pumpStatus = '已停止';
                             statusClass = 'stopped';
                             statusIcon = '<div class="status-icon just-stopped"></div>';
                         } else if (!wasActive && isActive) {
                             pumpStatus = '已启动';
                             statusClass = 'started';
                             statusIcon = '<div class="status-icon just-started"></div>';
                             animationClass = 'pump-active pump-just-started';
                         } else {
                             pumpStatus = isActive ? '运行中' : '已停止';
                             statusClass = isActive ? 'active' : 'inactive';
                             statusIcon = isActive ? 
                                 '<div class="status-icon running"></div>' : 
                                 '<div class="status-icon stopped"></div>';
                             animationClass = isActive ? 'pump-active' : '';
                         }
                     }
                     
                     html += `
                         <div class="pump-item ${statusClass} ${animationClass}">
                             <div class="pump-icon">
                                 <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                     <circle cx="12" cy="12" r="10"></circle>
                                     <path d="M12 16v-4"></path>
                                     <path d="M12 8h.01"></path>
                                 </svg>
                             </div>
                             <div class="pump-name">${item.name}</div>
                             <div class="pump-status-container">
                                 ${statusIcon}
                                 <div class="pump-status">${pumpStatus}</div>
                             </div>
                         </div>
                     `;
                 });
                 
                 html += `
                         </div>
                     </div>
                 `;
             }
             
             // 关闭容器
             html += `
                     </div>
                 </div>
             `;
             
             // 添加增强样式
             html += `
                 <style>
                     .data-card {
                         background: #fff;
                         border-radius: 16px;
                         box-shadow: 0 8px 20px rgba(0,0,0,0.08);
                         overflow: hidden;
                         margin-bottom: 20px;
                         font-family: 'Poppins', -apple-system, sans-serif;
                         transition: all 0.3s ease;
                         transform: translateZ(0);
                     }
                     
                     .data-card:hover {
                         box-shadow: 0 12px 28px rgba(0,0,0,0.12);
                         transform: translateY(-2px);
                     }
                     
                     .data-header {
                         padding: 16px 18px;
                         color: white;
                         display: flex;
                         justify-content: space-between;
                         align-items: center;
                         position: relative;
                         overflow: hidden;
                     }
                     
                     .data-header.normal-header {
                         background: linear-gradient(135deg, #2196f3, #21cbf3);
                     }
                     
                     .data-header.warning-header {
                         background: linear-gradient(135deg, #ff9800, #ff5722);
                     }
                     
                     .data-header::after {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: 0;
                         width: 100%;
                         height: 100%;
                         background: linear-gradient(rgba(255,255,255,0.1), rgba(255,255,255,0));
                         z-index: 1;
                     }
                     
                     .data-title {
                         font-weight: 600;
                         font-size: 17px;
                         position: relative;
                         z-index: 2;
                     }
                     
                     .data-timestamp {
                         font-size: 12px;
                         opacity: 0.9;
                         position: relative;
                         z-index: 2;
                         background: rgba(255,255,255,0.2);
                         padding: 3px 8px;
                         border-radius: 12px;
                     }
                     
                     .data-body {
                         padding: 16px;
                     }
                     
                     .data-section {
                         margin-bottom: 20px;
                         animation: fadeIn 0.5s ease-out;
                     }
                     
                     @keyframes fadeIn {
                         from { opacity: 0; transform: translateY(10px); }
                         to { opacity: 1; transform: translateY(0); }
                     }
                     
                     .data-section:last-child {
                         margin-bottom: 0;
                     }
                     
                     .section-title {
                         font-size: 15px;
                         color: #555;
                         margin-bottom: 12px;
                         padding-left: 8px;
                         border-left: 4px solid #2196f3;
                         font-weight: 500;
                     }
                     
                     /* 传感器样式 */
                     .sensor-grid {
                         display: grid;
                         grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                         gap: 12px;
                     }
                     
                     .sensor-item {
                         display: flex;
                         padding: 14px;
                         border-radius: 12px;
                         background: #f8f9fa;
                         align-items: center;
                         box-shadow: 0 2px 8px rgba(0,0,0,0.03);
                         transition: all 0.3s ease;
                         border: 1px solid rgba(0,0,0,0.04);
                     }
                     
                     .sensor-item:hover {
                         transform: translateY(-2px);
                         box-shadow: 0 4px 12px rgba(0,0,0,0.08);
                     }
                     
                     .sensor-item.warning {
                         background: linear-gradient(to bottom right, #fff8e1, #fffde7);
                         border: 1px solid #ffd600;
                         animation: pulse 2s infinite;
                     }
                     
                     @keyframes pulse {
                         0% { box-shadow: 0 0 0 0 rgba(255, 214, 0, 0.4); }
                         70% { box-shadow: 0 0 0 6px rgba(255, 214, 0, 0); }
                         100% { box-shadow: 0 0 0 0 rgba(255, 214, 0, 0); }
                     }
                     
                     .sensor-icon {
                         font-size: 22px;
                         margin-right: 12px;
                         width: 36px;
                         height: 36px;
                         border-radius: 50%;
                         background: rgba(33, 150, 243, 0.15);
                         display: flex;
                         align-items: center;
                         justify-content: center;
                         box-shadow: 0 2px 6px rgba(33, 150, 243, 0.2);
                     }
                     
                     .ph-sensor .sensor-icon {
                         background: rgba(156, 39, 176, 0.15);
                         color: #9c27b0;
                         box-shadow: 0 2px 6px rgba(156, 39, 176, 0.2);
                     }
                     
                     .water-sensor .sensor-icon {
                         background: rgba(33, 150, 243, 0.15);
                         color: #2196f3;
                         box-shadow: 0 2px 6px rgba(33, 150, 243, 0.2);
                     }
                     
                     .sensor-details {
                         flex: 1;
                     }
                     
                     .sensor-name {
                         font-weight: 500;
                         font-size: 14px;
                         color: #333;
                         display: flex;
                         align-items: center;
                     }
                     
                     .warning-badge {
                         margin-left: 6px;
                         font-size: 14px;
                     }
                     
                     .warning-badge.pulse {
                         animation: badgePulse 1.5s infinite;
                     }
                     
                     @keyframes badgePulse {
                         0% { transform: scale(1); }
                         50% { transform: scale(1.2); }
                         100% { transform: scale(1); }
                     }
                     
                     .sensor-value {
                         font-size: 16px;
                         margin-top: 6px;
                     }
                     
                     .value-change {
                         display: flex;
                         align-items: center;
                     }
                     
                     .value-old {
                         text-decoration: line-through;
                         opacity: 0.6;
                         font-size: 13px;
                     }
                     
                     .arrow {
                         margin: 0 5px;
                         opacity: 0.7;
                         font-size: 13px;
                     }
                     
                     .value-new {
                         font-weight: 600;
                         font-size: 16px;
                     }
                     
                     .value-current {
                         font-weight: 600;
                         font-size: 16px;
                     }
                     
                     .value-low {
                         color: #e53935;
                     }
                     
                     .value-high {
                         color: #ff9800;
                     }
                     
                     .value-normal {
                         color: #43a047;
                     }
                     
                     .trend-up {
                         color: #4caf50;
                         margin-left: 4px;
                         font-weight: bold;
                         animation: slideUp 0.5s ease;
                     }
                     
                     @keyframes slideUp {
                         from { transform: translateY(5px); opacity: 0; }
                         to { transform: translateY(0); opacity: 1; }
                     }
                     
                     .trend-down {
                         color: #f44336;
                         margin-left: 4px;
                         font-weight: bold;
                         animation: slideDown 0.5s ease;
                     }
                     
                     @keyframes slideDown {
                         from { transform: translateY(-5px); opacity: 0; }
                         to { transform: translateY(0); opacity: 1; }
                     }
                     
                     /* 水泵样式 */
                     .pump-grid {
                         display: grid;
                         grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                         gap: 12px;
                     }
                     
                     .pump-item {
                         background: #f8f9fa;
                         border-radius: 12px;
                         padding: 12px 8px;
                         display: flex;
                         flex-direction: column;
                         align-items: center;
                         text-align: center;
                         box-shadow: 0 2px 8px rgba(0,0,0,0.03);
                         transition: all 0.3s ease;
                         position: relative;
                         overflow: hidden;
                     }
                     
                     .pump-item:hover {
                         transform: translateY(-2px);
                     }
                     
                     .pump-item.active {
                         background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
                         border: 1px solid rgba(129, 199, 132, 0.5);
                     }
                     
                     .pump-item.inactive {
                         background: linear-gradient(135deg, #eceff1, #cfd8dc);
                         border: 1px solid rgba(176, 190, 197, 0.5);
                     }
                     
                     .pump-item.started {
                         background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
                         border: 1px solid #81c784;
                         animation: highlight 2s;
                     }
                     
                     @keyframes highlight {
                         0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.6); }
                         70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
                         100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
                     }
                     
                     .pump-item.stopped {
                         background: linear-gradient(135deg, #ffebee, #ffcdd2);
                         border: 1px solid #e57373;
                     }
                     
                     .pump-active::before {
                         content: '';
                         position: absolute;
                         top: -50%;
                         left: -50%;
                         width: 200%;
                         height: 200%;
                         background: radial-gradient(circle, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0) 70%);
                         animation: pumpPulse 2s linear infinite;
                     }
                     
                     @keyframes pumpPulse {
                         0% { transform: scale(0.5); opacity: 0.7; }
                         100% { transform: scale(1); opacity: 0; }
                     }
                     
                     .pump-just-started::after {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: 0;
                         right: 0;
                         bottom: 0;
                         background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
                         animation: startFlash 1s ease-out;
                     }
                     
                     @keyframes startFlash {
                         0% { opacity: 0.8; }
                         100% { opacity: 0; }
                     }
                     
                     .pump-icon {
                         margin-bottom: 8px;
                         position: relative;
                         z-index: 2;
                     }
                     
                     .pump-item.active .pump-icon,
                     .pump-item.started .pump-icon {
                         color: #2e7d32;
                         filter: drop-shadow(0 2px 3px rgba(46, 125, 50, 0.3));
                     }
                     
                     .pump-item.inactive .pump-icon,
                     .pump-item.stopped .pump-icon {
                         color: #c62828;
                         filter: drop-shadow(0 2px 3px rgba(198, 40, 40, 0.2));
                     }
                     
                     .pump-name {
                         font-size: 12px;
                         font-weight: 500;
                         color: #333;
                         margin-bottom: 6px;
                         position: relative;
                         z-index: 2;
                     }
                     
                     .pump-status-container {
                         display: flex;
                         align-items: center;
                         justify-content: center;
                         position: relative;
                         z-index: 2;
                     }
                     
                     .status-icon {
                         width: 8px;
                         height: 8px;
                         border-radius: 50%;
                         margin-right: 5px;
                     }
                     
                     .status-icon.running {
                         background-color: #4caf50;
                         box-shadow: 0 0 0 rgba(76, 175, 80, 0.4);
                         animation: statusPulse 2s infinite;
                     }
                     
                     .status-icon.stopped {
                         background-color: #f44336;
                     }
                     
                     .status-icon.just-started {
                         background-color: #4caf50;
                         animation: startPulse 1s infinite;
                     }
                     
                     .status-icon.just-stopped {
                         background-color: #f44336;
                         animation: stopPulse 1s infinite;
                     }
                     
                     @keyframes statusPulse {
                         0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4); }
                         70% { box-shadow: 0 0 0 6px rgba(76, 175, 80, 0); }
                         100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
                     }
                     
                     @keyframes startPulse {
                         0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
                         70% { box-shadow: 0 0 0 8px rgba(76, 175, 80, 0); }
                         100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
                     }
                     
                     @keyframes stopPulse {
                         0% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7); }
                         70% { box-shadow: 0 0 0 8px rgba(244, 67, 54, 0); }
                         100% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0); }
                     }
                     
                     .pump-status {
                         font-size: 11px;
                         color: #666;
                         background: rgba(255,255,255,0.6);
                         padding: 2px 8px;
                         border-radius: 10px;
                         box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                     }
                     
                     .pump-item.active .pump-status,
                     .pump-item.started .pump-status {
                         color: #2e7d32;
                         font-weight: 500;
                     }
                     
                     .pump-item.stopped .pump-status {
                         color: #c62828;
                         font-weight: 500;
                     }
                 </style>
             `;
             
             return html;
         }
         
         // 保留旧版格式化方法用于兼容
         function formatDataSummary(dataItems, title, isWarning = false) {
             // 转换数据格式
             const formattedData = dataItems.map(item => {
                 // 如果是字符串格式的旧数据
                 if (typeof item === 'string') {
                     return {
                         name: item.split(':')[0].trim(),
                         value: item.split(':').slice(1).join(':').trim(),
                         isWarning: isWarning
                     };
                 }
                 return item;
             });
             
             // 使用新的增强版格式化方法
             return formatEnhancedDataSummary(formattedData, title);
         }
        
        // 开始获取数据
        function startFetchingData() {
            // 立即获取一次数据
            fetchDeviceData();
            
            // 清除现有的定时器
            if (timer) {
                clearInterval(timer);
            }
            
            // 设置定期获取数据
            timer = setInterval(() => {
                fetchDeviceData();
            }, refreshPeriod);
        }
        
        // 手动刷新数据
        function manualRefresh() {
            fetchDeviceData();
            showToast('正在刷新数据...', 'info');
        }
        
        // 从OneNET获取设备数据
        function fetchDeviceData() {
            // 显示加载状态
            showLoadingState(true);
            
            try {
                // 生成token
                let token = '';
                try {
                    const params = {
                        author_key: 'xaVmoFXwf9oB4QpVN8Vt8sL4hqhLoIyRp31g2j0gQKEt0VG5XEFbpYGvQst14YPX',
                        version: '2022-05-01',
                        user_id: '420568',
                    };
                    token = createCommonToken(params);
                } catch (tokenError) {
                    console.error('生成token失败:', tokenError);
                    showLoadingState(false);
                    addNotification('生成token失败: ' + tokenError.message, 'error');
                    return;
                }
                
                // 发送请求获取设备数据
                fetch('https://iot-api.heclouds.com/thingmodel/query-device-property?product_id=HTJ98Pjh4a&device_name=flotation', {
                    method: 'GET',
                    headers: {
                        'Authorization': token,
                        'Content-Type': 'application/json'
                    },
                    cache: 'no-cache'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取设备数据失败: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    showLoadingState(false);
                    if (data && data.data) {
                        processData(data.data);
                        showApiStatus(true);
                    } else {
                        console.error('返回数据格式异常:', data);
                        addNotification('返回数据格式异常', 'error');
                        showApiStatus(false);
                    }
                })
                .catch(error => {
                    showLoadingState(false);
                    console.error('获取数据失败:', error);
                    addNotification('获取数据失败: ' + error.message, 'error');
                    showApiStatus(false);
                });
            } catch (error) {
                showLoadingState(false);
                console.error('请求异常:', error);
                addNotification('请求异常: ' + error.message, 'error');
                showApiStatus(false);
            }
        }
        
        // 显示API状态指示器
        function showApiStatus(isSuccess) {
            if (isSuccess) {
                // API连接成功
                showToast('✓ API连接正常', 'success');
            } else {
                // API连接失败
                showToast('✗ API连接失败', 'error');
            }
        }
        
        // 显示或隐藏加载状态
        function showLoadingState(isLoading) {
            // 使用具有ID的toast，便于后续识别和移除
            const loadingToastId = 'loadingStateToast';
            
            // 移除任何现有的加载提示
            const existingToast = document.getElementById(loadingToastId);
            if (existingToast) {
                existingToast.classList.remove('show');
                setTimeout(() => {
                    if (existingToast.parentNode) {
                        existingToast.parentNode.removeChild(existingToast);
                    }
                }, 300);
            }
            
            if (isLoading) {
                // 确保存在toast容器
                let toastContainer = document.querySelector('.toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.className = 'toast-container';
                    document.body.appendChild(toastContainer);
                }
                
                // 创建加载提示toast
                const loadingToast = document.createElement('div');
                loadingToast.className = 'toast-message info';
                loadingToast.id = loadingToastId;
                
                // 创建加载图标
                const spinnerSvg = `
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#2196f3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loading-svg-icon">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 6v2"></path>
                </svg>
                <style>
                    @keyframes loading-rotate {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    .loading-svg-icon {
                        animation: loading-rotate 1s linear infinite;
                    }
                </style>
                `;
                
                // 设置内容
                loadingToast.innerHTML = `
                    <div class="toast-icon">${spinnerSvg}</div>
                    <div class="toast-content">正在更新设备数据...</div>
                `;
                
                // 添加到容器
                toastContainer.appendChild(loadingToast);
                
                // 显示动画
                setTimeout(() => {
                    loadingToast.classList.add('show');
                }, 10);
            }
        }
        

        

        
                          // 设备名称映射
         const deviceNames = {
             'PH': 'PH值',
             'Water': '水位',
             'Pump1': '捕收剂阳',
             'Pump2': '捕收剂阴',
             'Pump3': '起泡剂',
             'Pump4': '活化剂',
             'Pump5': '抑制剂',
             'Pump6': '调整剂'
         };
         
         // 处理接口返回的数据
         function processData(newData) {
             if (!newData || !Array.isArray(newData)) {
                 addNotification('数据为空或格式异常', 'error');
                 return;
             }
             
             // 打印日志调试
             console.log('获取到的原始数据:', JSON.stringify(newData));
             
             // 修复API返回数据格式：如果返回的是带有identifier字段的结构，转换为需要的格式
             let processedData = newData;
             if (newData.length > 0 && typeof newData[0].identifier !== 'undefined') {
                 // 处理带有identifier结构的数据
                 const relevantData = [];
                 
                 // 找出所有需要的设备数据
                 const targetIds = ['PH', 'Water', 'Pump1', 'Pump2', 'Pump3', 'Pump4', 'Pump5', 'Pump6'];
                 
                 targetIds.forEach(id => {
                     const item = newData.find(d => d.identifier === id);
                     if (item) {
                         relevantData.push(item);
                     }
                 });
                 
                 processedData = relevantData;
             }
             
             // 过滤掉 Pump7, Pump8, Pump9 的数据
             processedData = processedData.filter(item => {
                 const id = item.identifier || item.id || '';
                 return !['Pump7', 'Pump8', 'Pump9'].includes(id);
             });
             
             console.log('处理后的数据:', JSON.stringify(processedData));
             
             // 检查是否有数据
             if (processedData.length === 0) {
                 addNotification('未找到有效的设备数据', 'error');
                 return;
             }
             
             // 首次加载打包展示
             if (!lastData) {
                 let changes = [];
                 
                 processedData.forEach(item => {
                     const identifier = item.identifier || item.id || '未知设备';
                     const name = deviceNames[identifier] || identifier;
                     
                     // 检查是否为异常值
                     let isWarning = false;
                     let warningMessage = '';
                     
                     if (identifier === 'PH' && (parseFloat(item.value) < 6.5 || parseFloat(item.value) > 8.5)) {
                         isWarning = true;
                         warningMessage = `超出正常范围(6.5-8.5)`;
                     } else if (identifier === 'Water' && parseFloat(item.value) < 20) {
                         isWarning = true;
                         warningMessage = `水位过低`;
                     }
                     
                     changes.push({
                         name,
                         value: item.value,
                         isNew: true,
                         isWarning,
                         warningMessage,
                         identifier
                     });
                 });
                 
                 // 打包为一条通知
                 if (changes.length > 0) {
                     const content = formatEnhancedDataSummary(changes, '设备数据更新');
                     addNotification(content, changes.some(item => item.isWarning) ? 'error' : 'normal');
                 }
                 
                 lastData = JSON.parse(JSON.stringify(processedData));
                 return;
             }
             
             // 与上一份数据对比，将变化打包为一条通知
             let changes = [];
             
             processedData.forEach((item) => {
                 const identifier = item.identifier || item.id || '未知设备';
                 const name = deviceNames[identifier] || identifier;
                 
                 // 检查该设备是否存在于上次数据中
                 const prevItem = lastData.find(prev => 
                     (prev.identifier && prev.identifier === item.identifier) ||
                     (prev.id && prev.id === item.id)
                 );
                 
                 // 检查是否为异常值
                 let isWarning = false;
                 let warningMessage = '';
                 
                 if (identifier === 'PH' && (parseFloat(item.value) < 6.5 || parseFloat(item.value) > 8.5)) {
                     isWarning = true;
                     warningMessage = `超出正常范围(6.5-8.5)`;
                 } else if (identifier === 'Water' && parseFloat(item.value) < 20) {
                     isWarning = true;
                     warningMessage = `水位过低`;
                 }
                 
                 if (!prevItem) {
                     // 新设备数据
                     changes.push({
                         name,
                         value: item.value,
                         isNew: true,
                         isWarning,
                         warningMessage,
                         identifier
                     });
                 } else if (prevItem.value !== item.value) {
                     // 值变化
                     changes.push({
                         name,
                         oldValue: prevItem.value,
                         newValue: item.value,
                         isChanged: true,
                         isWarning,
                         warningMessage,
                         identifier
                     });
                 }
             });
             
             // 添加数据变化通知（如果有）
             if (changes.length > 0) {
                 const content = formatEnhancedDataSummary(changes, '设备数据更新');
                 addNotification(content, changes.some(item => item.isWarning) ? 'error' : 'changed');
             }
             
             // 更新上一次的数据
             lastData = JSON.parse(JSON.stringify(processedData));
         }
        
        // 添加新通知
        function addNotification(content, type = 'normal') {
            const notification = {
                id: Date.now(),
                time: new Date().toLocaleTimeString(),
                content,
                type
            };
            
            // 添加到通知列表开头
            notifications.unshift(notification);
            
            // 限制最大通知数量
            if (notifications.length > MAX_NOTIFICATIONS) {
                notifications.pop();
            }
            
            // 保存到本地存储
            saveNotifications();
            
            // 更新UI
            renderNotifications();
            
            // 显示提示
            showToast(`新通知: ${content.substring(0, 20)}${content.length > 20 ? '...' : ''}`, type);
        }
        
        // 清空所有通知
        function clearNotifications() {
            if (confirm('确定要清空所有通知吗？')) {
                notifications = [];
                saveNotifications();
                renderNotifications();
                showToast('所有通知已清空', 'info');
            }
        }
        
        // 改变通知周期
        function changePeriod() {
            const select = document.getElementById('periodSelect');
            refreshPeriod = parseInt(select.value);
            
            // 保存到本地存储
            localStorage.setItem('notificationPeriod', refreshPeriod);
            
            // 重新启动定时获取数据
            startFetchingData();
            
            // 显示提示
            showToast(`通知周期已更改为 ${refreshPeriod/1000} 秒`, 'info');
        }
        
        // 显示Toast提示
        function showToast(message, type = 'info') {
            // 检查是否已有toast容器
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container';
                document.body.appendChild(toastContainer);
            }
            
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast-message ${type}`;
            
            // 根据类型设置图标
            let icon = '';
            switch (type) {
                case 'success':
                    icon = `<svg class="toast-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>`;
                    break;
                case 'error':
                    icon = `<svg class="toast-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="15" y1="9" x2="9" y2="15"></line>
                                <line x1="9" y1="9" x2="15" y2="15"></line>
                            </svg>`;
                    break;
                default:
                    icon = `<svg class="toast-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="16" x2="12" y2="12"></line>
                                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                            </svg>`;
            }
            
            toast.innerHTML = icon + message;
            toastContainer.appendChild(toast);
            
            // 显示toast
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);
            
            // 3秒后自动消失
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
        
        // 返回上一页
        function goBack() {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
