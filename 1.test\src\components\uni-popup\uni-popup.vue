<template>
  <view v-if="showPopup" class="uni-popup" :class="[position, mode, popupstyle, ani]" @touchmove.stop.prevent="clear">
    <view class="uni-popup__mask" :class="[ani]" @click="onTap"></view>
    <view class="uni-popup__wrapper" :class="[position, ani]" @click.stop="clear">
      <slot />
    </view>
  </view>
</template>

<script>
export default {
  name: 'UniPopup',
  props: {
    // 开启动画
    animation: {
      type: Boolean,
      default: true
    },
    // 弹出层类型，可选值：top/bottom/center
    type: {
      type: String,
      default: 'center'
    },
    // 是否开启遮罩
    mask: {
      type: Boolean,
      default: true
    },
    // 点击遮罩关闭弹框
    maskClick: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showPopup: false,
      position: this.type,
      ani: '',
      popupstyle: '',
      mode: ''
    };
  },
  methods: {
    open() {
      this.showPopup = true;
      this.$nextTick(() => {
        this.ani = 'ani-open';
      });
    },
    close(type) {
      this.ani = '';
      this.$emit('close', {
        type: type || '',
        open: false
      });
      setTimeout(() => {
        this.showPopup = false;
      }, 300);
    },
    onTap() {
      if (!this.maskClick) return;
      this.close();
    },
    clear(e) {
      // 点击穿透处理
      e.stopPropagation();
      e.preventDefault();
    }
  }
};
</script>

<style>
.uni-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.uni-popup__mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
}

.uni-popup__wrapper {
  position: absolute;
  background-color: #ffffff;
  border-radius: 15px;
  overflow: hidden;
}

.ani-open {
  transition: all 0.3s ease-out;
  opacity: 1;
}

.uni-popup .center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.uni-popup .top {
  top: 0;
  left: 0;
  right: 0;
}

.uni-popup .bottom {
  bottom: 0;
  left: 0;
  right: 0;
}
</style>