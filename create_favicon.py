#!/usr/bin/env python3
"""
创建favicon.ico文件
"""

from PIL import Image, ImageDraw
import os

def create_favicon():
    """创建favicon图标"""
    size = 32
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制简化的辐射监测图标
    center = size // 2
    
    # 背景圆
    draw.ellipse([2, 2, size-2, size-2], fill=(0, 180, 216, 255))
    
    # 内圆
    inner_radius = 8
    draw.ellipse([center-inner_radius, center-inner_radius, 
                  center+inner_radius, center+inner_radius], 
                 outline=(255, 255, 255, 200), width=2)
    
    # 辐射符号（简化版）
    # 中心点
    draw.ellipse([center-2, center-2, center+2, center+2], fill=(255, 215, 0, 255))
    
    # 三个辐射扇形（简化为线条）
    import math
    for i in range(3):
        angle = i * 120
        angle_rad = math.radians(angle)
        x1 = center + 6 * math.cos(angle_rad)
        y1 = center + 6 * math.sin(angle_rad)
        x2 = center + 12 * math.cos(angle_rad)
        y2 = center + 12 * math.sin(angle_rad)
        draw.line([(x1, y1), (x2, y2)], fill=(255, 215, 0, 255), width=2)
    
    return img

def main():
    """主函数"""
    favicon = create_favicon()
    
    # 创建多尺寸的ICO文件
    sizes = [16, 32]
    images = []
    
    for size in sizes:
        resized = favicon.resize((size, size), Image.Resampling.LANCZOS)
        images.append(resized)
    
    # 保存为ICO文件
    images[0].save('src/static/favicon.ico', format='ICO', sizes=[(16, 16), (32, 32)])
    
    print("Favicon创建完成: src/static/favicon.ico")

if __name__ == "__main__":
    main()
