<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>个人中心</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            background: none;
        }
        
        /* 主页面设计 */
        .my-container {
            width: 100%;
            max-width: 375px;
            height: auto;
            aspect-ratio: 375/812;
            background: linear-gradient(145deg, #f0f0f0, #fafafa);
            border-radius: 50px;
            overflow: hidden;
            box-shadow: 0 20px 60px -10px rgba(30, 136, 229, 0.18);
            position: relative;
            display: flex;
            flex-direction: column;
            max-height: 92vh;
        }
        
        /* 顶部导航栏 */
        .top-nav {
            display: flex;
            justify-content: space-between;
            padding: 16px 20px 10px;
            align-items: center;
            position: relative;
            z-index: 10;
            background-color: #f0f0f0;
        }
        
        .back-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .back-button svg {
            width: 20px;
            height: 20px;
            color: #333;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #222;
        }
        
        .action-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #222;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .action-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .action-button svg {
            width: 18px;
            height: 18px;
            color: white;
        }
        
        /* 用户资料部分 */
        .user-profile {
            padding: 20px;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border-radius: 20px;
            margin: 0 20px 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .user-profile::before {
            content: '';
            position: absolute;
            top: -20px;
            right: -20px;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(33, 150, 243, 0.05) 0%, rgba(33, 150, 243, 0) 70%);
            border-radius: 50%;
            z-index: 0;
        }
        
        .user-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background-color: #e0e0e0;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 16px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            border: 3px solid #fff;
            position: relative;
            overflow: hidden;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-avatar .edit-avatar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            font-size: 10px;
            text-align: center;
            padding: 2px 0;
            cursor: pointer;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .user-id {
            font-size: 12px;
            color: #666;
            display: flex;
            align-items: center;
        }
        
        .user-id svg {
            width: 14px;
            height: 14px;
            margin-right: 4px;
        }
        
        .edit-profile {
            background-color: #f5f5f5;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 12px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 8px;
        }
        
        .edit-profile:hover {
            background-color: #e0e0e0;
        }
        
        /* 设置内容区域 */
        .settings-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px;
            background-color: white;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
        }
        
        .settings-content::-webkit-scrollbar {
            width: 4px;
        }
        
        .settings-content::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .settings-content::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 20px;
        }
        
        .settings-section {
            margin-top: 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            padding-top: 10px;
            display: flex;
            align-items: center;
        }
        
        .section-title svg {
            width: 18px;
            height: 18px;
            margin-right: 8px;
            color: #2196f3;
        }
        
        .settings-item {
            background-color: #f9f9f9;
            border-radius: 12px;
            padding: 14px 16px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
            border: 1px solid rgba(0, 0, 0, 0.03);
        }
        
        .settings-item:hover {
            background-color: #f0f0f0;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }
        
        .settings-item-left {
            display: flex;
            align-items: center;
        }
        
        .settings-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            background-color: rgba(33, 150, 243, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 12px;
        }
        
        .settings-icon svg {
            width: 20px;
            height: 20px;
            color: #2196f3;
        }
        
        .settings-text {
            display: flex;
            flex-direction: column;
        }
        
        .settings-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .settings-description {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        .settings-item-right {
            display: flex;
            align-items: center;
        }
        
        .settings-value {
            font-size: 14px;
            color: #666;
            margin-right: 8px;
        }
        
        .arrow-icon {
            width: 16px;
            height: 16px;
            color: #999;
        }
        
        /* 开关样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        input:checked + .toggle-slider {
            background-color: #2196f3;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { transform: translateY(10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .animate-fadeIn {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .animate-slideUp {
            animation: slideUp 0.5s ease-in-out;
        }
        
        /* 版本信息徽章 */
        .version-badge {
            background-color: rgba(76, 175, 80, 0.1);
            color: #4caf50;
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 500;
        }
        
        /* 退出登录按钮 */
        .logout-button {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 14px;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            margin-top: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(244, 67, 54, 0.3);
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
        }
        
        .logout-button:hover {
            background-color: #d32f2f;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(244, 67, 54, 0.4);
        }
        
        .logout-button svg {
            width: 18px;
            height: 18px;
        }
        
        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 100;
        }
        
        .modal-content {
            background-color: white;
            border-radius: 16px;
            padding: 24px;
            width: 80%;
            max-width: 300px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            animation: slideUp 0.3s ease-out;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .modal-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .modal-input:focus {
            border-color: #2196f3;
            outline: none;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
        }
        
        .modal-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        .modal-button {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .cancel-button {
            background-color: #f0f0f0;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .cancel-button:hover {
            background-color: #e0e0e0;
        }
        
        .confirm-button {
            background-color: #2196f3;
            color: white;
            border: none;
            box-shadow: 0 2px 5px rgba(33, 150, 243, 0.3);
        }
        
        .confirm-button:hover {
            background-color: #1976d2;
            box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
        }
        
        /* 频率选择样式 */
        .frequency-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 16px;
        }
        
        .frequency-option {
            display: flex;
            align-items: center;
            padding: 10px 12px;
            background-color: #f5f5f5;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .frequency-option:hover {
            background-color: #e0e0e0;
        }
        
        .frequency-option input {
            margin-right: 10px;
        }
        
        .frequency-option span {
            font-size: 14px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 个人中心页面 -->
        <div class="my-container">
            <!-- 顶部导航栏 -->
            <div class="top-nav">
                <div class="back-button" id="backButton">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="19" y1="12" x2="5" y2="12"></line>
                        <polyline points="12 19 5 12 12 5"></polyline>
                    </svg>
                </div>
                <div class="page-title">个人中心</div>
                <div class="action-button" id="helpButton">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                    </svg>
                </div>
            </div>
            
            <!-- 用户资料部分 -->
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
                    <div class="edit-avatar">编辑</div>
                </div>
                <div class="user-info">
                    <div class="user-name">张工程师</div>
                    <div class="user-id">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        ID: 1001234
                    </div>
                    <div class="edit-profile">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                        编辑资料
                    </div>
                </div>
            </div>
            
            <!-- 设置内容区域 -->
            <div class="settings-content">
                <!-- 账户安全设置 -->
                <div class="settings-section">
                    <div class="section-title">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        </svg>
                        账户与安全
                    </div>
                    
                    <div class="settings-item" id="changePasswordBtn">
                        <div class="settings-item-left">
                            <div class="settings-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">修改密码</div>
                                <div class="settings-description">定期更新密码以保护账户安全</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <svg class="arrow-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="settings-item" id="privacySettingsBtn">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(156, 39, 176, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#9c27b0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">隐私设置</div>
                                <div class="settings-description">管理您的个人隐私选项</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <svg class="arrow-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <!-- 数据设置 -->
                <div class="settings-section">
                    <div class="section-title">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                        数据与通知
                    </div>
                    
                    <div class="settings-item">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(33, 150, 243, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#2196f3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12 6 12 12 16 14"></polyline>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">数据更新频率</div>
                                <div class="settings-description">选择您希望数据刷新的频率</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <div class="settings-value data-frequency-value">每5分钟</div>
                            <svg class="arrow-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(255, 152, 0, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#ff9800" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                                    <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">数据更新通知</div>
                                <div class="settings-description">在数据更新时接收通知</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(76, 175, 80, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#4caf50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">后台数据同步</div>
                                <div class="settings-description">允许应用在后台同步数据</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 应用设置 -->
                <div class="settings-section">
                    <div class="section-title">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                        应用与关于
                    </div>
                    
                    <div class="settings-item">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(233, 30, 99, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#e91e63" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="2" y1="12" x2="22" y2="12"></line>
                                    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">语言设置</div>
                                <div class="settings-description">更改应用显示语言</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <div class="settings-value">简体中文</div>
                            <svg class="arrow-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(0, 188, 212, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#00bcd4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="7"></circle>
                                    <polyline points="12 9 12 12 13.5 13.5"></polyline>
                                    <path d="M16.51 17.35l-.35 3.83a2 2 0 0 1-2 1.82H9.83a2 2 0 0 1-2-1.82l-.35-3.83m.01-10.7l.35-3.83A2 2 0 0 1 9.83 1h4.35a2 2 0 0 1 2 1.82l.35 3.83"></path>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">当前版本</div>
                                <div class="settings-description">查看应用版本信息</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <div class="version-badge">v2.3.5</div>
                        </div>
                    </div>
                    
                    <div class="settings-item" id="checkUpdateBtn">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(121, 85, 72, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#795548" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21.2 15c.7-1.2 1-2.5.7-3.9-.6-2-2.4-3.5-4.4-3.5h-1.2c-.7-3-3.2-5.2-6.2-5.6-3-.3-5.9 1.3-7.3 4-1.2 2.5-1 6.5.5 8.8m8.7-1.6V21"></path>
                                    <path d="M16 16l-4-4-4 4"></path>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">检查更新</div>
                                <div class="settings-description">检查应用最新版本</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <svg class="arrow-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <!-- 法律与帮助 -->
                <div class="settings-section">
                    <div class="section-title">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                            <line x1="12" y1="17" x2="12.01" y2="17"></line>
                        </svg>
                        法律与帮助
                    </div>
                    
                    <div class="settings-item" id="userAgreementBtn">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(96, 125, 139, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#607d8b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14 2 14 8 20 8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10 9 9 9 8 9"></polyline>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">用户协议</div>
                                <div class="settings-description">查看用户协议条款</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <svg class="arrow-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="settings-item" id="privacyPolicyBtn">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(158, 158, 158, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#9e9e9e" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">隐私政策</div>
                                <div class="settings-description">查看数据使用与隐私条款</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <svg class="arrow-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="settings-item" id="contactSupportBtn">
                        <div class="settings-item-left">
                            <div class="settings-icon" style="background-color: rgba(255, 87, 34, 0.1);">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#ff5722" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                    <line x1="9" y1="10" x2="15" y2="10"></line>
                                    <line x1="12" y1="7" x2="12" y2="13"></line>
                                </svg>
                            </div>
                            <div class="settings-text">
                                <div class="settings-label">联系客服</div>
                                <div class="settings-description">获取问题解答与帮助</div>
                            </div>
                        </div>
                        <div class="settings-item-right">
                            <svg class="arrow-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <!-- 退出登录按钮 -->
                <button class="logout-button" id="logoutBtn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                        <polyline points="16 17 21 12 16 7"></polyline>
                        <line x1="21" y1="12" x2="9" y2="12"></line>
                    </svg>
                    退出登录
                </button>
            </div>
        </div>
        
        <!-- 修改密码弹窗 -->
    <div class="modal" id="changePasswordModal">
        <div class="modal-content animate-slideUp">
            <div class="modal-title">修改密码</div>
            <input type="password" class="modal-input" placeholder="当前密码" id="currentPassword">
            <input type="password" class="modal-input" placeholder="新密码" id="newPassword">
            <input type="password" class="modal-input" placeholder="确认新密码" id="confirmPassword">
            <div class="modal-buttons">
                <div class="modal-button cancel-button" id="cancelChangePassword">取消</div>
                <div class="modal-button confirm-button" id="confirmChangePassword">确认</div>
            </div>
        </div>
    </div>

    <!-- 数据频率选择弹窗 -->
    <div class="modal" id="dataFrequencyModal">
        <div class="modal-content animate-slideUp">
            <div class="modal-title">选择数据更新频率</div>
            <div class="frequency-options">
                <label class="frequency-option">
                    <input type="radio" name="frequency" value="1分钟">
                    <span>每1分钟</span>
                </label>
                <label class="frequency-option">
                    <input type="radio" name="frequency" value="5分钟" checked>
                    <span>每5分钟</span>
                </label>
                <label class="frequency-option">
                    <input type="radio" name="frequency" value="10分钟">
                    <span>每10分钟</span>
                </label>
                <label class="frequency-option">
                    <input type="radio" name="frequency" value="30分钟">
                    <span>每30分钟</span>
                </label>
                <label class="frequency-option">
                    <input type="radio" name="frequency" value="1小时">
                    <span>每1小时</span>
                </label>
            </div>
            <div class="modal-buttons">
                <div class="modal-button cancel-button" id="cancelFrequency">取消</div>
                <div class="modal-button confirm-button" id="confirmFrequency">确认</div>
            </div>
        </div>
    </div>

    <!-- 退出确认弹窗 -->
    <div class="modal" id="logoutConfirmModal">
        <div class="modal-content animate-slideUp">
            <div class="modal-title">确认退出登录</div>
            <p style="text-align: center; margin-bottom: 20px;">您确定要退出登录吗？</p>
            <div class="modal-buttons">
                <div class="modal-button cancel-button" id="cancelLogout">取消</div>
                <div class="modal-button confirm-button" id="confirmLogout">确认</div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 返回按钮
            document.getElementById('backButton').addEventListener('click', function() {
                window.location.href = 'home.html';
            });
            
            // 修改密码弹窗
            const changePasswordModal = document.getElementById('changePasswordModal');
            const changePasswordBtn = document.getElementById('changePasswordBtn');
            const cancelChangePassword = document.getElementById('cancelChangePassword');
            const confirmChangePassword = document.getElementById('confirmChangePassword');
            
            changePasswordBtn.addEventListener('click', function() {
                changePasswordModal.style.display = 'flex';
            });
            
            cancelChangePassword.addEventListener('click', function() {
                changePasswordModal.style.display = 'none';
                clearPasswordInputs();
            });
            
            confirmChangePassword.addEventListener('click', function() {
                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                
                if (!currentPassword || !newPassword || !confirmPassword) {
                    alert('请填写所有密码字段');
                    return;
                }
                
                if (newPassword !== confirmPassword) {
                    alert('新密码与确认密码不一致');
                    return;
                }
                
                // 这里添加修改密码的逻辑
                alert('密码修改成功！');
                changePasswordModal.style.display = 'none';
                clearPasswordInputs();
            });
            
            function clearPasswordInputs() {
                document.getElementById('currentPassword').value = '';
                document.getElementById('newPassword').value = '';
                document.getElementById('confirmPassword').value = '';
            }
            
            // 数据更新频率弹窗
            const dataFrequencyModal = document.getElementById('dataFrequencyModal');
            const frequencyItem = document.querySelector('.settings-item:nth-child(2)');
            const frequencyValue = document.querySelector('.data-frequency-value');
            const cancelFrequency = document.getElementById('cancelFrequency');
            const confirmFrequency = document.getElementById('confirmFrequency');
            
            frequencyItem.addEventListener('click', function() {
                dataFrequencyModal.style.display = 'flex';
                
                // 设置当前选中的频率
                const currentValue = frequencyValue.textContent;
                document.querySelectorAll('input[name="frequency"]').forEach(radio => {
                    if (radio.value === currentValue) {
                        radio.checked = true;
                    }
                });
            });
            
            cancelFrequency.addEventListener('click', function() {
                dataFrequencyModal.style.display = 'none';
            });
            
            confirmFrequency.addEventListener('click', function() {
                const selectedOption = document.querySelector('input[name="frequency"]:checked');
                if (selectedOption) {
                    frequencyValue.textContent = selectedOption.value;
                }
                dataFrequencyModal.style.display = 'none';
            });
            
            // 退出登录确认弹窗
            const logoutConfirmModal = document.getElementById('logoutConfirmModal');
            const logoutBtn = document.getElementById('logoutBtn');
            const cancelLogout = document.getElementById('cancelLogout');
            const confirmLogout = document.getElementById('confirmLogout');
            
            logoutBtn.addEventListener('click', function() {
                logoutConfirmModal.style.display = 'flex';
            });
            
            cancelLogout.addEventListener('click', function() {
                logoutConfirmModal.style.display = 'none';
            });
            
            confirmLogout.addEventListener('click', function() {
                // 执行退出登录逻辑
                window.location.href = 'index.html';
            });
            
            // 用户协议按钮
            document.getElementById('userAgreementBtn').addEventListener('click', function() {
                window.location.href = 'agreement.html';
            });
            
            // 检查更新按钮
            document.getElementById('checkUpdateBtn').addEventListener('click', function() {
                alert('正在检查更新...\n当前已是最新版本！');
            });
            
            // 帮助按钮
            document.getElementById('helpButton').addEventListener('click', function() {
                alert('如需帮助，请联系客服或查看用户手册。');
            });
            
            // 关闭点击空白区域的弹窗
            window.addEventListener('click', function(event) {
                if (event.target === changePasswordModal) {
                    changePasswordModal.style.display = 'none';
                    clearPasswordInputs();
                }
                
                if (event.target === dataFrequencyModal) {
                    dataFrequencyModal.style.display = 'none';
                }
                
                if (event.target === logoutConfirmModal) {
                    logoutConfirmModal.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html> 