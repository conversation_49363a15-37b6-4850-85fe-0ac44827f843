import { createCommonToken } from '@/key.js';

const DEFAULT_PERIOD = 5000; // 默认 5 秒
const MAX_NOTIFICATIONS = 100; // 最大通知数量

class NotificationService {
  constructor() {
    // 周期（毫秒）
    this.period = uni.getStorageSync('notificationPeriod') || DEFAULT_PERIOD;
    // 上一次获取到的原始数据，用于变化对比
    this.lastData = null;
    // 通知列表
    this.notifications = [];
    try {
      const savedNotifications = uni.getStorageSync('notifications');
      if (savedNotifications) {
        this.notifications = savedNotifications;
      }
    } catch (e) {
      console.error('解析保存的通知失败:', e);
    }
    
    // 定时器 id
    this.timer = null;
  }

  // 启动通知服务
  start() {
    if (this.timer) {
      return; // 已经启动过
    }
    
    try {
      // 生成 token
      const params = {
        author_key: 'xaVmoFXwf9oB4QpVN8Vt8sL4hqhLoIyRp31g2j0gQKEt0VG5XEFbpYGvQst14YPX',
        version: '2022-05-01',
        user_id: '420568',
      };
      this.token = createCommonToken(params);

      // 立即拉取一次，并开始周期任务
      this.fetchData();
      this.timer = setInterval(() => {
        this.fetchData();
      }, this.period);
    } catch (e) {
      console.error('启动通知服务失败:', e);
      this.addNotification('启动通知服务失败: ' + e.message, 'error');
    }
  }

  // 停止通知服务
  stop() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  // 修改通知周期
  setPeriod(newPeriod) {
    this.period = newPeriod;
    uni.setStorageSync('notificationPeriod', newPeriod);
    // 重新启动定时器
    this.stop();
    this.start();
  }

  // 获取通知列表
  getNotifications() {
    return this.notifications;
  }

  // 清空所有通知
  clearNotifications() {
    this.notifications = [];
    uni.setStorageSync('notifications', []);
    uni.$emit('notificationsUpdated', this.notifications);
  }

  // 保存通知到存储
  saveNotifications() {
    uni.setStorageSync('notifications', this.notifications);
  }

  // 添加一条通知
  addNotification(content, type = 'normal') {
    const notification = {
      id: Date.now(),
      time: new Date().toLocaleTimeString(),
      content,
      type // normal | changed | error
    };
    
    // 添加到通知列表开头
    this.notifications.unshift(notification);
    
    // 限制最大通知数量
    if (this.notifications.length > MAX_NOTIFICATIONS) {
      this.notifications.pop();
    }
    
    // 保存到存储
    this.saveNotifications();
    
    // 广播通知更新事件
    uni.$emit('notificationsUpdated', this.notifications);
    
    // 显示通知提示
    uni.showToast({
      title: '收到新通知',
      icon: 'none'
    });
  }

  // 设备名称映射
  getDeviceName(identifier) {
    const deviceNames = {
      'PH': 'PH值',
      'Water': '水位',
      'Pump1': '捕收剂阳',
      'Pump2': '捕收剂阴',
      'Pump3': '起泡剂',
      'Pump4': '活化剂',
      'Pump5': '抑制剂',
      'Pump6': '调整剂'
    };
    return deviceNames[identifier] || identifier;
  }

  // 处理接口返回的数据
  processData(newData) {
    if (!newData || !Array.isArray(newData)) {
      this.addNotification('数据为空或格式异常', 'error');
      return;
    }
    
    // 修复API返回数据格式
    let processedData = newData;
    if (newData.length > 0 && typeof newData[0].identifier !== 'undefined') {
      const relevantData = [];
      
      // 找出所有需要的设备数据
      const targetIds = ['PH', 'Water', 'Pump1', 'Pump2', 'Pump3', 'Pump4', 'Pump5', 'Pump6'];
      
      targetIds.forEach(id => {
        const item = newData.find(d => d.identifier === id);
        if (item) {
          relevantData.push(item);
        }
      });
      
      processedData = relevantData;
    }
    
    // 过滤掉不需要的数据
    processedData = processedData.filter(item => {
      const id = item.identifier || item.id || '';
      return !['Pump7', 'Pump8', 'Pump9'].includes(id);
    });
    
    // 检查是否有数据
    if (processedData.length === 0) {
      this.addNotification('未找到有效的设备数据', 'error');
      return;
    }
    
    // 首次加载打包展示
    if (!this.lastData) {
      let changes = [];
      
      processedData.forEach(item => {
        const identifier = item.identifier || item.id || '未知设备';
        const name = this.getDeviceName(identifier);
        
        // 检查是否为异常值
        let isWarning = false;
        let warningMessage = '';
        
        if (identifier === 'PH' && (parseFloat(item.value) < 6.5 || parseFloat(item.value) > 8.5)) {
          isWarning = true;
          warningMessage = `超出正常范围(6.5-8.5)`;
        } else if (identifier === 'Water' && parseFloat(item.value) < 20) {
          isWarning = true;
          warningMessage = `水位过低`;
        }
        
        changes.push({
          name,
          value: item.value,
          isNew: true,
          isWarning,
          warningMessage,
          identifier
        });
      });
      
      // 生成数据摘要通知
      if (changes.length > 0) {
        const content = this.formatDataSummary(changes, '设备数据更新');
        this.addNotification(content, changes.some(item => item.isWarning) ? 'error' : 'normal');
      }
      
      this.lastData = JSON.parse(JSON.stringify(processedData));
      return;
    }
    
    // 与上一份数据对比，将变化打包为一条通知
    let changes = [];
    
    processedData.forEach((item) => {
      const identifier = item.identifier || item.id || '未知设备';
      const name = this.getDeviceName(identifier);
      
      // 检查该设备是否存在于上次数据中
      const prevItem = this.lastData.find(prev => 
        (prev.identifier && prev.identifier === item.identifier) ||
        (prev.id && prev.id === item.id)
      );
      
      // 检查是否为异常值
      let isWarning = false;
      let warningMessage = '';
      
      if (identifier === 'PH' && (parseFloat(item.value) < 6.5 || parseFloat(item.value) > 8.5)) {
        isWarning = true;
        warningMessage = `超出正常范围(6.5-8.5)`;
      } else if (identifier === 'Water' && parseFloat(item.value) < 20) {
        isWarning = true;
        warningMessage = `水位过低`;
      }
      
      if (!prevItem) {
        // 新设备数据
        changes.push({
          name,
          value: item.value,
          isNew: true,
          isWarning,
          warningMessage,
          identifier
        });
      } else if (prevItem.value !== item.value) {
        // 值变化
        changes.push({
          name,
          oldValue: prevItem.value,
          newValue: item.value,
          isChanged: true,
          isWarning,
          warningMessage,
          identifier
        });
      }
    });
    
    // 添加数据变化通知
    if (changes.length > 0) {
      const content = this.formatDataSummary(changes, '设备数据更新');
      this.addNotification(content, changes.some(item => item.isWarning) ? 'error' : 'changed');
    }
    
    // 更新上一次的数据
    this.lastData = JSON.parse(JSON.stringify(processedData));
  }

  // 美化数据摘要展示
  formatDataSummary(dataItems, title) {
    let html = `<div style="padding:10px 0;">${title}</div>`;
    
    // 分类统计变化
    const changes = {
      sensors: dataItems.filter(item => item.name === 'PH值' || item.name === '水位'),
      pumps: dataItems.filter(item => 
        item.name.includes('捕收剂') || 
        item.name.includes('起泡剂') || 
        item.name.includes('活化剂') || 
        item.name.includes('抑制剂') || 
        item.name.includes('调整剂')
      )
    };
    
    // 添加传感器数据
    if (changes.sensors.length > 0) {
      html += `<div style="margin-top:8px;"><b>传感器数据:</b></div>`;
      
      changes.sensors.forEach(item => {
        const isWarning = item.isWarning ? 
          `<span style="color:#e74c3c;margin-left:5px;">⚠️ ${item.warningMessage || '异常'}</span>` : '';
        
        if (item.isNew) {
          html += `<div style="margin:5px 0;padding:5px;background:#f8f9fa;border-radius:5px;">
            ${item.name}: <b>${item.value}</b> ${isWarning}
          </div>`;
        } else if (item.isChanged) {
          const trend = parseFloat(item.newValue) > parseFloat(item.oldValue) ? '↑' : 
                       (parseFloat(item.newValue) < parseFloat(item.oldValue) ? '↓' : '');
          const trendColor = trend === '↑' ? '#4caf50' : (trend === '↓' ? '#e74c3c' : '');
          
          html += `<div style="margin:5px 0;padding:5px;background:#f8f9fa;border-radius:5px;">
            ${item.name}: <span style="text-decoration:line-through;color:#999;">${item.oldValue}</span>
            → <b>${item.newValue}</b> 
            <span style="color:${trendColor};font-weight:bold;">${trend}</span>
            ${isWarning}
          </div>`;
        }
      });
    }
    
    // 添加水泵状态
    if (changes.pumps.length > 0) {
      html += `<div style="margin-top:8px;"><b>水泵状态:</b></div>`;
      
      changes.pumps.forEach(item => {
        let status, statusColor;
        
        if (item.isNew) {
          const isActive = item.value === 'true';
          status = isActive ? '运行中' : '已停止';
          statusColor = isActive ? '#4caf50' : '#e74c3c';
        } else if (item.isChanged) {
          const wasActive = item.oldValue === 'true';
          const isActive = item.newValue === 'true';
          
          if (wasActive && !isActive) {
            status = '已停止';
            statusColor = '#e74c3c';
          } else if (!wasActive && isActive) {
            status = '已启动';
            statusColor = '#4caf50';
          } else {
            status = isActive ? '运行中' : '已停止';
            statusColor = isActive ? '#4caf50' : '#e74c3c';
          }
        }
        
        html += `<div style="margin:5px 0;padding:5px;background:#f8f9fa;border-radius:5px;">
          ${item.name}: <span style="color:${statusColor};font-weight:500;">${status}</span>
        </div>`;
      });
    }
    
    return html;
  }

  // 发起网络请求获取数据
  fetchData() {
    // 生成新的 token
    try {
      const params = {
        author_key: 'xaVmoFXwf9oB4QpVN8Vt8sL4hqhLoIyRp31g2j0gQKEt0VG5XEFbpYGvQst14YPX',
        version: '2022-05-01',
        user_id: '420568',
      };
      this.token = createCommonToken(params);
    } catch (e) {
      console.error('生成token失败:', e);
      this.addNotification('生成token失败: ' + e.message, 'error');
      return;
    }
    
    // 请求数据
    uni.request({
      url: 'https://iot-api.heclouds.com/thingmodel/query-device-property',
      method: 'GET',
      data: {
        product_id: 'HTJ98Pjh4a',
        device_name: 'flotation',
      },
      header: {
        'Authorization': this.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.data && res.data.data) {
          this.processData(res.data.data);
        } else {
          console.error('返回数据格式异常:', res.data);
          this.addNotification('返回数据格式异常', 'error');
        }
      },
      fail: (err) => {
        console.error('获取数据失败:', err);
        this.addNotification('获取数据失败: ' + (err.errMsg || '网络错误'), 'error');
      }
    });
  }
}

// 单例导出
export default new NotificationService();