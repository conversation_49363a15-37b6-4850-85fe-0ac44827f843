#!/usr/bin/env python3
"""
智能辐射监测APP图标生成器
将SVG图标转换为不同尺寸的PNG格式
"""

import os
import base64
from PIL import Image, ImageDraw, ImageFilter
import io

def create_app_icon(size):
    """创建指定尺寸的APP图标"""
    # 创建画布
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 计算比例
    scale = size / 512
    
    # 绘制圆角矩形背景
    corner_radius = int(112 * scale)
    
    # 创建渐变背景
    for y in range(size):
        for x in range(size):
            # 计算渐变颜色
            progress = (x + y) / (2 * size)
            r = int(0 + (0 - 0) * progress)
            g = int(180 + (150 - 180) * progress)
            b = int(216 + (182 - 216) * progress)
            
            # 检查是否在圆角矩形内
            if is_in_rounded_rect(x, y, size, size, corner_radius):
                img.putpixel((x, y), (r, g, b, 255))
    
    # 添加光晕效果
    overlay = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    overlay_draw = ImageDraw.Draw(overlay)
    
    # 绘制径向渐变光晕
    center_x, center_y = size // 2, int(size * 0.3)
    max_radius = int(size * 0.7)
    
    for radius in range(max_radius, 0, -1):
        alpha = int(100 * (1 - radius / max_radius) * 0.4)
        overlay_draw.ellipse(
            [center_x - radius, center_y - radius, center_x + radius, center_y + radius],
            fill=(255, 255, 255, alpha)
        )
    
    img = Image.alpha_composite(img, overlay)
    
    # 绘制手表轮廓
    watch_radius = int(120 * scale)
    center = size // 2
    
    # 外圈
    draw = ImageDraw.Draw(img)
    stroke_width = max(1, int(8 * scale))
    draw.ellipse(
        [center - watch_radius, center - watch_radius, 
         center + watch_radius, center + watch_radius],
        outline=(255, 255, 255, 230), width=stroke_width
    )
    
    # 内圈
    inner_radius = int(100 * scale)
    inner_stroke = max(1, int(2 * scale))
    draw.ellipse(
        [center - inner_radius, center - inner_radius,
         center + inner_radius, center + inner_radius],
        fill=(255, 255, 255, 25), outline=(255, 255, 255, 150), width=inner_stroke
    )
    
    # 绘制辐射符号背景
    radiation_center_y = center - int(16 * scale)
    radiation_bg_radius = int(45 * scale)
    draw.ellipse(
        [center - radiation_bg_radius, radiation_center_y - radiation_bg_radius,
         center + radiation_bg_radius, radiation_center_y + radiation_bg_radius],
        fill=(255, 215, 0, 50)
    )
    
    # 绘制辐射符号
    draw_radiation_symbol(draw, center, radiation_center_y, scale)
    
    # 绘制文字
    text_y = center + int(64 * scale)
    text_width = int(60 * scale)
    text_height = int(16 * scale)
    
    # 文字背景
    draw.rounded_rectangle(
        [center - text_width//2, text_y - text_height//2,
         center + text_width//2, text_y + text_height//2],
        radius=int(8 * scale), fill=(255, 255, 255, 230)
    )
    
    # 添加阴影效果
    shadow = img.filter(ImageFilter.GaussianBlur(radius=2))
    final_img = Image.new('RGBA', (size + 10, size + 10), (0, 0, 0, 0))
    final_img.paste(shadow, (5, 8), shadow)
    final_img.paste(img, (5, 5), img)
    
    # 裁剪回原始尺寸
    final_img = final_img.crop((5, 5, size + 5, size + 5))
    
    return final_img

def is_in_rounded_rect(x, y, width, height, radius):
    """检查点是否在圆角矩形内"""
    # 检查四个角
    if x < radius and y < radius:
        return (x - radius) ** 2 + (y - radius) ** 2 <= radius ** 2
    elif x >= width - radius and y < radius:
        return (x - (width - radius)) ** 2 + (y - radius) ** 2 <= radius ** 2
    elif x < radius and y >= height - radius:
        return (x - radius) ** 2 + (y - (height - radius)) ** 2 <= radius ** 2
    elif x >= width - radius and y >= height - radius:
        return (x - (width - radius)) ** 2 + (y - (height - radius)) ** 2 <= radius ** 2
    else:
        return True

def draw_radiation_symbol(draw, center_x, center_y, scale):
    """绘制辐射符号"""
    # 中心圆
    center_radius = int(8 * scale)
    draw.ellipse(
        [center_x - center_radius, center_y - center_radius,
         center_x + center_radius, center_y + center_radius],
        fill=(255, 215, 0, 255)
    )
    
    # 简化的辐射扇形（使用三角形近似）
    outer_radius = int(35 * scale)
    inner_radius = int(17.5 * scale)
    
    # 三个扇形区域
    import math
    for i in range(3):
        angle = i * 120 + 90  # 从顶部开始，每120度一个
        angle_rad = math.radians(angle)
        
        # 计算扇形的点
        x1 = center_x + outer_radius * math.cos(angle_rad - math.radians(30))
        y1 = center_y + outer_radius * math.sin(angle_rad - math.radians(30))
        x2 = center_x + outer_radius * math.cos(angle_rad + math.radians(30))
        y2 = center_y + outer_radius * math.sin(angle_rad + math.radians(30))
        x3 = center_x + inner_radius * math.cos(angle_rad + math.radians(30))
        y3 = center_y + inner_radius * math.sin(angle_rad + math.radians(30))
        x4 = center_x + inner_radius * math.cos(angle_rad - math.radians(30))
        y4 = center_y + inner_radius * math.sin(angle_rad - math.radians(30))
        
        draw.polygon([(x1, y1), (x2, y2), (x3, y3), (x4, y4)], fill=(255, 215, 0, 255))

def main():
    """主函数"""
    # 创建图标目录
    icon_dir = "src/static/icons"
    os.makedirs(icon_dir, exist_ok=True)
    
    # 生成不同尺寸的图标
    sizes = [16, 32, 48, 64, 128, 256, 512, 1024]
    
    for size in sizes:
        print(f"生成 {size}x{size} 图标...")
        icon = create_app_icon(size)
        icon.save(f"{icon_dir}/app-icon-{size}.png", "PNG")
    
    # 生成主图标
    main_icon = create_app_icon(512)
    main_icon.save("src/static/app-icon.png", "PNG")
    
    print("所有图标生成完成！")
    print("文件位置：")
    print("- 主图标: src/static/app-icon.png")
    print("- 其他尺寸: src/static/icons/app-icon-*.png")

if __name__ == "__main__":
    main()
