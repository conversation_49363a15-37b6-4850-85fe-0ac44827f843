<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>设备历史数据</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-moment@1.0.1/dist/chartjs-adapter-moment.min.js"></script>
    <!-- 添加jQuery依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- 添加日期范围选择器库 -->
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker@3.1.0/daterangepicker.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker@3.1.0/daterangepicker.css">
    <!-- 确保加载key.js -->
    <script src="key.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            background: none;
        }
        
        /* 主页面设计 */
        .history-container {
            width: 100%;
            max-width: 390px;
            height: auto;
            aspect-ratio: 390/844;
            background: linear-gradient(145deg, #f0f0f0, #fafafa);
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 20px 60px -10px rgba(30, 136, 229, 0.18);
            position: relative;
            display: flex;
            flex-direction: column;
            max-height: 92vh;
        }
        
        /* 顶部导航栏 */
        .top-nav {
            display: flex;
            justify-content: space-between;
            padding: 16px 20px 10px;
            align-items: center;
            position: relative;
            z-index: 10;
            background-color: #f0f0f0;
        }
        
        .back-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .back-button svg {
            width: 20px;
            height: 20px;
            color: #333;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #222;
        }
        
        .share-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #222;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .share-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .share-button svg {
            width: 18px;
            height: 18px;
            color: white;
        }
        
        /* 设备选择器 */
        .device-selector-container {
            padding: 0 20px 12px;
            overflow: hidden;
            position: relative;
        }
        
        .device-selector {
            display: flex;
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            scrollbar-width: none;
            gap: 12px;
            padding: 4px 0;
            margin-bottom: 8px;
            -webkit-overflow-scrolling: touch; /* 更流畅的滚动效果 */
        }
        
        .device-selector::-webkit-scrollbar {
            display: none;
        }
        
        .device-option {
            background-color: white;
            border-radius: 14px;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            scroll-snap-align: start;
            display: flex;
            align-items: center;
            gap: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .device-option.active {
            background-color: #222;
            color: white;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        
        /* 添加波纹效果 */
        .device-option::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%, -50%);
            transform-origin: 50% 50%;
        }
        
        .device-option:active::after {
            animation: ripple 0.6s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }
        
        .device-option-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .device-option-icon svg {
            width: 16px;
            height: 16px;
        }
        
        .device-option.active .device-option-icon {
            color: white;
        }
        
        /* 滚动指示器 */
        .scroll-indicator {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 4px;
            padding: 4px 0;
        }
        
        .indicator-dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: #ddd;
        }
        
        .indicator-dot.active {
            background-color: #222;
            width: 12px;
            border-radius: 3px;
        }
        
        /* 设备信息卡片 */
        .device-info-card {
            background-color: white;
            border-radius: 16px;
            margin: 0 20px 16px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            animation: fadeIn 0.5s ease-in-out;
            position: relative;
            overflow: hidden;
        }
        
        .device-info-card::before {
            content: '';
            position: absolute;
            top: -20px;
            right: -20px;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(33, 150, 243, 0.05) 0%, rgba(33, 150, 243, 0) 70%);
            border-radius: 50%;
        }
        
        .device-info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }
        
        .device-icon-large {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            background: linear-gradient(145deg, #f0f0f0, #fafafa);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            flex-shrink: 0;
        }
        
        .device-icon-large svg {
            width: 32px;
            height: 32px;
        }
        
        .device-details-large {
            flex: 1;
        }
        
        .device-name-large {
            font-size: 18px;
            font-weight: 600;
            color: #222;
            margin-bottom: 4px;
        }
        
        .device-status-large {
            font-size: 14px;
            font-weight: 500;
            padding: 4px 12px;
            border-radius: 12px;
            background-color: #f0f0f0;
            display: inline-block;
        }
        
        .device-status-large.running {
            color: #4caf50;
            background-color: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.2);
        }
        
        .device-status-large.stopped {
            color: #f44336;
            background-color: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.2);
        }
        
        .device-status-large.sensor-value {
            color: #2196f3;
            background-color: rgba(33, 150, 243, 0.1);
            border: 1px solid rgba(33, 150, 243, 0.2);
        }
        
        /* 日期范围选择器 */
        .date-range-selector {
            padding: 16px 20px;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            margin: 0 20px 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            position: relative;
            z-index: 5;
        }
        
        .date-range-input {
            display: flex;
            margin-bottom: 12px;
        }
        
        #dateRangePicker {
            flex: 1;
            padding: 10px 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            font-size: 14px;
            background-color: white;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            margin-right: 10px;
        }
        
        #dateRangePicker:focus {
            outline: none;
            border-color: #2196f3;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
        }
        
        .search-btn {
            padding: 10px 20px;
            background-color: #2196f3;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(33, 150, 243, 0.3);
        }
        
        .search-btn:hover {
            background-color: #1976d2;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
        }
        
        .quick-date-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .quick-option {
            padding: 6px 12px;
            background-color: white;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .quick-option.active {
            background-color: #222;
            color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }
        
        /* 波纹效果 */
        @keyframes ripple {
            0% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 0.5;
            }
            100% {
                transform: translate(-50%, -50%) scale(20);
                opacity: 0;
            }
        }
        
        .ripple-effect {
            animation: ripple 0.6s ease-out;
        }
        
        /* 添加波纹效果 */
        .quick-option::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%, -50%);
            transform-origin: 50% 50%;
        }
        
        .quick-option:active::after {
            animation: ripple 0.6s ease-out;
        }
        
        /* 日期选择器弹出框样式 */
        .daterangepicker {
            font-family: 'Poppins', sans-serif;
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .daterangepicker .calendar-table {
            border-radius: 10px;
        }
        
        .daterangepicker td.active, .daterangepicker td.active:hover {
            background-color: #2196f3;
        }
        
        /* 历史内容区域 */
        .history-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px;
            background-color: white;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
        }
        
        .history-content::-webkit-scrollbar {
            width: 4px;
        }
        
        .history-content::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .history-content::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 20px;
        }
        
        .data-section {
            margin-top: 20px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-top: 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .view-toggle {
            display: flex;
            background-color: #f0f0f0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        }
        
        .toggle-option {
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .toggle-option.active {
            background-color: #222;
            color: white;
        }
        
        .toggle-option::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%, -50%);
            transform-origin: 50% 50%;
        }
        
        .toggle-option:active::after {
            animation: ripple 0.6s ease-out;
        }
        
        /* 布尔值历史记录矩阵 */
        .boolean-matrix {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            margin-bottom: 24px;
            transform-origin: center;
            animation: scaleIn 0.5s ease-out;
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 16px;
            padding: 16px;
            background-color: rgba(255, 255, 255, 0.8);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .boolean-matrix:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        @keyframes scaleIn {
            0% { transform: scale(0.95); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .matrix-cell {
            aspect-ratio: 1/1;
            border-radius: 6px;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px;
            color: #999;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .matrix-cell:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            z-index: 1;
        }
        
        .matrix-cell.active {
            background-color: #4caf50;
            color: white;
        }
        
        .matrix-cell.inactive {
            background-color: #f44336;
            color: white;
        }
        
        /* 矩阵日历头部 */
        .matrix-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 6px;
            margin-bottom: 6px;
            background-color: rgba(0, 0, 0, 0.03);
            border-radius: 10px;
            padding: 5px;
        }
        
        .matrix-header-cell {
            text-align: center;
            font-size: 11px;
            font-weight: 600;
            color: #555;
            padding: 5px 0;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 5px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        /* 日期指示器 */
        .day-indicator {
            grid-column: 1 / -1;
            text-align: center;
            font-size: 12px;
            font-weight: 600;
            color: #333;
            padding: 8px 0;
            margin: 5px 0;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        /* 泵设备和传感器设备的不同颜色 */
        .device-pump .matrix-cell.active {
            background-color: #4caf50;
            box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.5), 0 2px 4px rgba(76, 175, 80, 0.2);
        }
        
        .device-pump .matrix-cell.inactive {
            background-color: #f44336;
            box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.5), 0 2px 4px rgba(244, 67, 54, 0.2);
        }
        
        .device-sensor .matrix-cell.active {
            background-color: #2196f3;
            box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.5), 0 2px 4px rgba(33, 150, 243, 0.2);
        }
        
        .device-sensor .matrix-cell.inactive {
            background-color: #ff9800;
            box-shadow: 0 0 0 1px rgba(255, 152, 0, 0.5), 0 2px 4px rgba(255, 152, 0, 0.2);
        }

        /* 移除矩阵格子状态指示小白点 */
        
        /* 无数据状态的矩阵格子 */
        .matrix-cell.no-data {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            color: #aaa;
            cursor: default;
        }
        
        .matrix-cell.no-data:hover {
            transform: scale(1.02);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .matrix-cell.no-data::before {
            display: none;
        }
        
        /* 图表容器 */
        .chart-container {
            height: 260px;
            margin-bottom: 24px;
            position: relative;
            border-radius: 18px;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 18px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .chart-container:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        /* 数据预测部分 */
        .prediction-section {
            margin-top: 16px;
            margin-bottom: 20px;
        }
        
        .prediction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .prediction-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .prediction-icon {
            width: 20px;
            height: 20px;
            margin-right: 6px;
            color: #2196f3;
        }
        
        .prediction-accuracy {
            font-size: 12px;
            color: #999;
        }
        
        .prediction-accuracy span {
            color: #4caf50;
            font-weight: 500;
        }
        
        /* 矩阵图例 */
        .matrix-legend {
            display: flex;
            justify-content: center;
            margin-top: 12px;
            gap: 24px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }
        
        .legend-color.active {
            background-color: #4caf50;
        }
        
        .legend-color.inactive {
            background-color: #f44336;
        }
        
        .legend-text {
            font-size: 12px;
            color: #666;
        }
        
        /* 数据分析概要 */
        .data-summary {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 16px;
            background: linear-gradient(145deg, #f5f8ff, #f0f0f0);
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
            border: 1px solid rgba(33, 150, 243, 0.1);
            position: relative;
            overflow: hidden;
            animation: slideUp 0.5s ease-out;
        }
        
        .data-summary::before {
            content: "";
            position: absolute;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(33, 150, 243, 0.05) 0%, rgba(33, 150, 243, 0) 70%);
            top: -50px;
            right: -50px;
            border-radius: 50%;
            z-index: 0;
        }
        
        .summary-row {
            display: flex;
            align-items: center;
            padding: 8px;
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }
        
        .summary-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .summary-label {
            font-size: 14px;
            color: #666;
            margin-right: 8px;
            flex: 1;
        }
        
        .summary-value {
            font-size: 14px;
            font-weight: 600;
            color: #2196f3;
            background-color: rgba(33, 150, 243, 0.1);
            padding: 4px 10px;
            border-radius: 12px;
            min-width: 60px;
            text-align: center;
        }
        
        /* 预测分析徽章 */
        .prediction-badge {
            background: linear-gradient(135deg, #9c27b0, #7b1fa2);
            color: white;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 12px;
            border-radius: 30px;
            box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .prediction-badge::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            to {
                left: 100%;
            }
        }
        
        /* 预测分析洞察 */
        .prediction-insight {
            margin-top: 16px;
            background: linear-gradient(145deg, #f0f7ff, #f8fbff);
            border-radius: 16px;
            padding: 18px;
            display: flex;
            gap: 14px;
            border-left: 4px solid #2196f3;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .prediction-insight:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.15);
        }
        
        .prediction-insight::after {
            content: "";
            position: absolute;
            bottom: -10px;
            right: -10px;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(33, 150, 243, 0.05) 0%, rgba(33, 150, 243, 0) 70%);
            border-radius: 50%;
            z-index: 0;
        }
        
        .insight-icon {
            width: 28px;
            height: 28px;
            color: #2196f3;
            flex-shrink: 0;
            background-color: rgba(33, 150, 243, 0.1);
            border-radius: 50%;
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(33, 150, 243, 0.2);
            z-index: 1;
        }
        
        .insight-text {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            z-index: 1;
            letter-spacing: 0.2px;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { transform: translateY(10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        @keyframes slideIn {
            from { transform: translateX(10px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .animate-fadeIn {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .animate-slideUp {
            animation: slideUp 0.5s ease-in-out;
        }
        
        .animate-slideIn {
            animation: slideIn 0.5s ease-in-out;
        }
        
        /* 加载指示器 */
        .loading-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 10;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .loading-indicator.show {
            opacity: 1;
            visibility: visible;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-top-color: #2196f3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 统计卡片 */
        .stat-card {
            background-color: #f8f9fa;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
        }
        
        .stat-info {
            display: flex;
            flex-direction: column;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .stat-icon svg {
            width: 20px;
            height: 20px;
        }
        
        .stat-card.up .stat-icon {
            color: #4caf50;
        }
        
        .stat-card.down .stat-icon {
            color: #f44336;
        }
        
        .stat-card.neutral .stat-icon {
            color: #2196f3;
        }
        
        /* 设计说明部分 */
        .design-notes {
            flex: 1;
            padding: 0 40px;
        }
        
        .design-notes h2 {
            font-size: 24px;
            margin-bottom: 24px;
            color: #000;
        }
        
        .design-note {
            margin-bottom: 24px;
        }
        
        .design-note h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #333;
        }
        
        .design-note p {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
        }
        
        /* 响应式设计 - 优化手机界面 */
        @media (max-width: 1000px) {
            .container {
                flex-direction: column;
                align-items: center;
                padding: 0;
            }
            .history-container {
                max-width: 100vw;
                max-height: 90vh;
                border-radius: 30px;
            }
            
            /* 改进设备选择器在平板上的显示 */
            .device-selector {
                padding: 8px 0;
                gap: 10px;
            }
            
            .device-option {
                padding: 8px 14px;
                font-size: 13px;
            }
        }
        
        @media (max-width: 480px) {
            /* 手机界面优化 */
            .history-container {
                border-radius: 18px;
                max-height: 100vh;
                height: 100vh;
                width: 100%;
            }
            
            /* 顶部导航栏优化 */
            .top-nav {
                padding: 15px 16px 10px;
            }
            
            .back-button, .share-button {
                width: 36px;
                height: 36px;
            }
            
            .page-title {
                font-size: 16px;
                font-weight: 600;
            }
            
            /* 设备选择器优化 */
            .device-selector {
                padding: 5px 0;
                gap: 8px;
            }
            
            .device-option {
                padding: 8px 12px;
                font-size: 12px;
                border-radius: 12px;
            }
            
            /* 设备信息卡片优化 */
            .device-info-card {
                margin: 0 16px 12px;
                padding: 14px;
            }
            
            /* 时间周期选择器优化 */
            .time-period-selector {
                margin: 0 16px 12px;
                padding: 12px 16px;
                justify-content: space-between;
            }
            
            .period-option {
                padding: 6px 12px;
                font-size: 12px;
                margin-right: 6px;
            }
            
            /* 内容区域优化 */
            .history-content {
                padding: 0 16px 16px;
            }
            
            /* 统计卡片优化 */
            .stat-card {
                padding: 10px;
                margin-bottom: 10px;
            }
            
            /* 矩阵优化 */
            .boolean-matrix {
                gap: 4px;
                padding: 8px;
            }
            
            .matrix-cell {
                font-size: 9px;
            }
            
            /* 图表容器优化 */
            .chart-container {
                height: 220px;
                margin-bottom: 16px;
                padding: 12px;
            }
            
            /* 数据统计优化 */
            .data-summary {
                gap: 10px;
                padding: 12px;
            }
            
            /* Toast消息优化 */
            .toast-message {
                min-width: 200px;
                max-width: 90%;
                font-size: 13px;
                padding: 10px 16px;
            }
        }
        
        /* 添加视觉引导指示器 */
        .scroll-guide {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            color: #666;
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.3s ease;
            z-index: 5;
        }
        
        .scroll-guide:hover {
            opacity: 1;
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 历史数据页面 -->
        <div class="history-container">
            <!-- 顶部导航栏 -->
            <div class="top-nav">
                <div class="back-button" id="backButton">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="19" y1="12" x2="5" y2="12"></line>
                        <polyline points="12 19 5 12 12 5"></polyline>
                    </svg>
                </div>
                <div class="page-title">历史数据</div>
                <div class="share-button">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="18" cy="5" r="3"></circle>
                        <circle cx="6" cy="12" r="3"></circle>
                        <circle cx="18" cy="19" r="3"></circle>
                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                    </svg>
                </div>
            </div>
            
            <!-- 设备选择滚轮 -->
            <div class="device-selector-container">
                <div class="device-selector" id="deviceSelector">
                    <!-- 设备选择器将通过JS动态生成 -->
                </div>
            </div>
            
            <!-- 设备信息卡片 -->
            <div class="device-info-card">
                <div class="device-icon-large" id="deviceIcon">
                    <!-- 设备图标将通过JS动态插入 -->
                </div>
                <div class="device-details-large">
                    <div class="device-name-large" id="deviceName">设备名称</div>
                    <div class="device-status-large" id="deviceStatus">设备状态</div>
                </div>
            </div>
            
            <!-- 时间范围选择器 -->
            <div class="date-range-selector" id="dateRangeSelector">
                <div class="date-range-input">
                    <input type="text" id="dateRangePicker" placeholder="选择日期范围" readonly>
                    <button class="search-btn" id="searchDataBtn">查询</button>
                </div>
                <div class="quick-date-options">
                    <span class="quick-option" data-range="1h">最近1小时</span>
                    <span class="quick-option" data-range="24h">最近24小时</span>
                    <span class="quick-option" data-range="7d">最近7天</span>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="history-content">
                <!-- 加载指示器 -->
                <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                    <div class="loading-spinner"></div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="data-section animate-slideUp" style="animation-delay: 0.1s">
                    <div class="section-header">
                        <div class="section-title">数据统计</div>
                        <div id="refreshStats" style="cursor: pointer; display: flex; align-items: center; gap: 4px; font-size: 12px; color: #666;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M23 4v6h-6"></path>
                                <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
                            </svg>
                            刷新
                        </div>
                    </div>
                    
                    <div class="stat-cards">
                        <div class="stat-card up">
                            <div class="stat-info">
                                <div class="stat-label">平均运行时间</div>
                                <div class="stat-value">16.4小时/天</div>
                            </div>
                            <div class="stat-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                                    <polyline points="17 6 23 6 23 12"></polyline>
                                </svg>
                            </div>
                        </div>
                        
                        <div class="stat-card down">
                            <div class="stat-info">
                                <div class="stat-label">故障频率</div>
                                <div class="stat-value">2.3次/月</div>
                            </div>
                            <div class="stat-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline>
                                    <polyline points="17 18 23 18 23 12"></polyline>
                                </svg>
                            </div>
                        </div>
                        
                        <div class="stat-card neutral">
                            <div class="stat-info">
                                <div class="stat-label">能效评分</div>
                                <div class="stat-value">93/100</div>
                            </div>
                            <div class="stat-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 20V10"></path>
                                    <path d="M12 20V4"></path>
                                    <path d="M6 20v-6"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 布尔型设备状态历史 -->
                <div id="booleanHistorySection" class="data-section animate-slideUp" style="animation-delay: 0.2s">
                    <div class="section-header">
                        <div class="section-title">运行状态历史</div>
                        <div class="time-info">最近24小时</div>
                    </div>
                    
                    <div class="boolean-matrix" id="statusMatrix">
                        <!-- 矩阵格子将通过JS动态生成 -->
                    </div>
                    
                    <!-- 运行状态图例 -->
                    <div class="matrix-legend">
                        <div class="legend-item">
                            <div class="legend-color active"></div>
                            <div class="legend-text">运行</div>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color inactive"></div>
                            <div class="legend-text">停止</div>
                        </div>
                    </div>
                </div>
                
                <!-- 数值型设备数据历史 -->
                <div id="numericHistorySection" class="data-section animate-slideUp" style="animation-delay: 0.3s; display: none;">
                    <div class="section-header">
                        <div class="section-title">数据历史记录</div>
                        <div class="view-toggle">
                            <div class="toggle-option active" data-view="line">折线图</div>
                            <div class="toggle-option" data-view="bar">柱状图</div>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <canvas id="dataChart"></canvas>
                    </div>
                    
                    <!-- 数据分析概要 -->
                    <div class="data-summary">
                        <div class="summary-row">
                            <div class="summary-label">平均值:</div>
                            <div class="summary-value" id="avgValue">-</div>
                        </div>
                        <div class="summary-row">
                            <div class="summary-label">最大值:</div>
                            <div class="summary-value" id="maxValue">-</div>
                        </div>
                        <div class="summary-row">
                            <div class="summary-label">最小值:</div>
                            <div class="summary-value" id="minValue">-</div>
                        </div>
                        <div class="summary-row">
                            <div class="summary-label">标准差:</div>
                            <div class="summary-value" id="stdValue">-</div>
                        </div>
                    </div>
                </div>
                
                <!-- 预测趋势 -->
                <div id="predictionSection" class="data-section animate-slideUp" style="animation-delay: 0.4s; display: none;">
                    <div class="section-header">
                        <div class="section-title">预测分析</div>
                        <div class="prediction-badge">AI分析</div>
                    </div>
                    
                    <div class="prediction-section">
                        <div class="prediction-header">
                            <div class="prediction-title">
                                <div class="prediction-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                    </svg>
                                </div>
                                未来7天趋势预测
                            </div>
                            <div class="prediction-accuracy">准确度: <span>95%</span></div>
                        </div>
                        
                        <div class="chart-container">
                            <canvas id="predictionChart"></canvas>
                        </div>
                        
                        <!-- 预测分析结果 -->
                        <div class="prediction-insight">
                            <div class="insight-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                </svg>
                            </div>
                            <div class="insight-text" id="predictionInsight">
                                根据历史数据分析，预计该设备在未来7天内将保持稳定运行状态，趋势略有上升。建议定期检查，确保设备性能最优。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 确保可以访问createCommonToken函数
        let createCommonTokenFunc;
        
        try {
            // 尝试直接引入Node.js模块
            const keyModule = require('./key.js');
            createCommonTokenFunc = keyModule.createCommonToken;
        } catch (error) {
            console.error('无法直接引入key.js模块，将使用脚本标签加载的版本');
            // 检查createCommonToken是否已经在window对象中(通过script标签引入)
            if (typeof createCommonToken !== 'undefined') {
                createCommonTokenFunc = createCommonToken;
            } else {
                // 错误处理
                console.error('无法加载createCommonToken函数');
                alert('认证模块加载失败，请刷新页面重试');
            }
        }
        
        // 获取正确的数据流ID
        function getCorrectDatastreamId(deviceId) {
            // 根据产品物模型信息映射设备ID到正确的标识符
            const idMapping = {
                'ph': 'PH',          // PH传感器对应物模型中的PH标识符
                'water': 'Water',     // 水位传感器对应物模型中的Water标识符
                'pump1': 'Pump1',     // 泵1对应物模型中的Pump1标识符
                'pump2': 'Pump2',
                'pump3': 'Pump3',
                'pump4': 'Pump4',
                'pump5': 'Pump5',
                'pump6': 'Pump6',
                'pump7': 'Pump7',
                'pump8': 'Pump8',
                'pump9': 'Pump9'
            };
            
            // 返回映射的标识符，如果没有找到则返回原始ID
            return idMapping[deviceId] || deviceId;
        }
        
        // 显示Toast消息，参考注册页面的提示样式
        function showToast(message, type = 'info') {
            // 移除已存在的Toast元素
            const existingToasts = document.querySelectorAll('.toast-message');
            existingToasts.forEach(toast => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            });
            
            // 创建新的Toast元素
            const toast = document.createElement('div');
            toast.className = 'toast-message';
            
            // 根据类型设置不同的样式
            let bgColor, iconSvg;
            switch(type) {
                case 'success':
                    bgColor = 'linear-gradient(135deg, #4CAF50, #2E7D32)';
                    iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>`;
                    break;
                case 'error':
                    bgColor = 'linear-gradient(135deg, #F44336, #B71C1C)';
                    iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>`;
                    break;
                case 'warning':
                    bgColor = 'linear-gradient(135deg, #FF9800, #E65100)';
                    iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>`;
                    break;
                default: // info
                    bgColor = 'linear-gradient(135deg, #2196F3, #0D47A1)';
                    iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px;"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>`;
            }
            
            // 设置Toast样式
            Object.assign(toast.style, {
                position: 'fixed',
                bottom: '70px',
                left: '50%',
                transform: 'translateX(-50%)',
                background: bgColor,
                color: '#fff',
                padding: '12px 20px',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                zIndex: '1000',
                fontSize: '14px',
                fontWeight: '500',
                opacity: '0',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                display: 'flex',
                alignItems: 'center',
                minWidth: '250px',
                maxWidth: '80%'
            });
            
            // 设置消息内容
            toast.innerHTML = `${iconSvg}<span>${message}</span>`;
            
            // 添加到DOM
            document.body.appendChild(toast);
            
            // 显示Toast并添加动画效果
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(-50%) translateY(0)';
            }, 10);
            
            // 3秒后隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(-50%) translateY(20px)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
        
        // 设备数据（从URL参数获取）
        let deviceData = {
            type: 'pump', // 或 'sensor'
            name: '捕收剂阳',
            status: '运行中',
            icon: 'gear',
            id: 'pump1'
        };
        
        // 所有设备列表
        const allDevices = [
            {
                type: 'pump',
                name: '捕收剂阳',
                status: '运行中',
                icon: 'gear',
                id: 'pump1'
            },
            {
                type: 'pump',
                name: '捕收剂阴',
                status: '已停止',
                icon: 'settings',
                id: 'pump2'
            },
            {
                type: 'pump',
                name: '起泡剂',
                status: '运行中',
                icon: 'plus',
                id: 'pump3'
            },
            {
                type: 'pump',
                name: '活化剂',
                status: '已停止',
                icon: 'star',
                id: 'pump4'
            },
            {
                type: 'pump',
                name: '抑制剂',
                status: '运行中',
                icon: 'minus',
                id: 'pump5'
            },
            {
                type: 'pump',
                name: '调整剂',
                status: '已停止',
                icon: 'refresh',
                id: 'pump6'
            },
            {
                type: 'sensor',
                name: 'PH传感器',
                status: '7.2',
                icon: 'info',
                id: 'ph'
            },
            {
                type: 'sensor',
                name: '水位传感器',
                status: '80%',
                icon: 'more',
                id: 'water'
            }
        ];
        
        // 设备图标映射
        const deviceIcons = {
            'gear': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>`,
            'settings': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20V10"></path><path d="M18 20V4"></path><path d="M6 20v-6"></path></svg>`,
            'plus': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>`,
            'star': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>`,
            'minus': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="8" y1="12" x2="16" y2="12"></line></svg>`,
            'refresh': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 4v6h-6"></path><path d="M1 20v-6h6"></path><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path></svg>`,
            'info': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>`,
            'more': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 3v18"></path><path d="M3 12h18"></path></svg>`
        };
        
        // 初始化设备选择器
        function initDeviceSelector() {
            const deviceSelector = document.getElementById('deviceSelector');
            deviceSelector.innerHTML = '';
            
            // 为每个设备创建选项
            allDevices.forEach(device => {
                const deviceOption = document.createElement('div');
                deviceOption.className = 'device-option';
                deviceOption.setAttribute('data-device-id', device.id);
                
                if (device.id === deviceData.id) {
                    deviceOption.classList.add('active');
                }
                
                deviceOption.innerHTML = `
                    <div class="device-option-icon">
                        ${deviceIcons[device.icon]}
                    </div>
                    <span>${device.name}</span>
                `;
                
                // 添加点击事件
                deviceOption.addEventListener('click', function() {
                    // 添加点击视觉反馈
                    this.classList.add('pulse-animation');
                    setTimeout(() => {
                        this.classList.remove('pulse-animation');
                    }, 500);
                    
                    // 如果已经是活动的，不执行任何操作
                    if (this.classList.contains('active')) {
                        return;
                    }
                    
                    // 更新URL
                    const url = new URL(window.location);
                    url.searchParams.set('device', device.id);
                    window.history.pushState({}, '', url);
                    
                    // 更新设备数据
                    changeDevice(device.id);
                    
                    // 更新选中状态
                    document.querySelectorAll('.device-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    this.classList.add('active');
                });
                
                deviceSelector.appendChild(deviceOption);
            });
            
            // 添加滚动指示器
            const scrollIndicator = document.createElement('div');
            scrollIndicator.className = 'scroll-indicator';
            
            // 添加指示点
            for (let i = 0; i < Math.ceil(allDevices.length / 3); i++) {
                const dot = document.createElement('div');
                dot.className = i === 0 ? 'indicator-dot active' : 'indicator-dot';
                scrollIndicator.appendChild(dot);
            }
            
            // 处理滚动事件
            deviceSelector.addEventListener('scroll', () => {
                const scrollPosition = deviceSelector.scrollLeft;
                const maxScroll = deviceSelector.scrollWidth - deviceSelector.clientWidth;
                const scrollRatio = scrollPosition / maxScroll;
                const dotsCount = scrollIndicator.childElementCount;
                
                // 计算当前活动点
                const activeDotIndex = Math.min(
                    dotsCount - 1, 
                    Math.floor(scrollRatio * dotsCount)
                );
                
                // 更新指示点状态
                Array.from(scrollIndicator.children).forEach((dot, index) => {
                    if (index === activeDotIndex) {
                        dot.classList.add('active');
                    } else {
                        dot.classList.remove('active');
                    }
                });
            });
            
            document.querySelector('.device-selector-container').appendChild(scrollIndicator);
            
            // 添加提示信息
            const selectorContainer = document.querySelector('.device-selector-container');
            const tooltip = document.createElement('div');
            tooltip.style.position = 'absolute';
            tooltip.style.top = '-10px';
            tooltip.style.left = '50%';
            tooltip.style.transform = 'translateX(-50%)';
            tooltip.style.fontSize = '10px';
            tooltip.style.color = '#666';
            tooltip.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
            tooltip.style.padding = '2px 8px';
            tooltip.style.borderRadius = '10px';
            tooltip.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            tooltip.style.opacity = '0';
            tooltip.style.transition = 'opacity 0.3s ease';
            tooltip.style.zIndex = '10';
            tooltip.textContent = '左右滑动选择设备';
            
            selectorContainer.style.position = 'relative';
            selectorContainer.appendChild(tooltip);
            
            setTimeout(() => {
                tooltip.style.opacity = '1';
            }, 1500);
            
            setTimeout(() => {
                tooltip.style.opacity = '0';
                setTimeout(() => {
                    tooltip.remove();
                }, 300);
            }, 5500);
        }
        
        // 从URL获取设备信息
        function getDeviceFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const deviceId = urlParams.get('device');
            
            if (!deviceId) {
                // 默认显示第一个设备
                changeDevice(allDevices[0].id);
                return;
            }
            
            // 查找匹配的设备
            const foundDevice = allDevices.find(d => d.id === deviceId);
            if (foundDevice) {
                changeDevice(deviceId);
            } else {
                // 如果找不到匹配的设备，默认显示第一个
                changeDevice(allDevices[0].id);
            }
        }
        
        // 切换设备
        function changeDevice(deviceId) {
            // 查找设备数据
            const device = allDevices.find(d => d.id === deviceId) || allDevices[0];
            
            // 设备信息卡片动画
            const deviceInfoCard = document.querySelector('.device-info-card');
            if (deviceInfoCard) {
                deviceInfoCard.style.opacity = '0';
                deviceInfoCard.style.transform = 'translateY(10px)';
                
                setTimeout(() => {
                    // 更新当前设备数据
                    deviceData = { ...device };
                    
                    // 更新设备信息显示
                    updateDeviceInfo();
                    
                    deviceInfoCard.style.opacity = '1';
                    deviceInfoCard.style.transform = 'translateY(0)';
                }, 300);
            } else {
                // 如果不存在设备卡片直接更新
                deviceData = { ...device };
                updateDeviceInfo();
            }
            
            // 销毁现有图表以避免冲突
            if (window.dataChart && typeof window.dataChart.destroy === 'function') {
                window.dataChart.destroy();
                window.dataChart = null;
            }
            if (window.predictionChart && typeof window.predictionChart.destroy === 'function') {
                window.predictionChart.destroy();
                window.predictionChart = null;
            }
            
            // 重新加载数据
            try {
                const period = document.querySelector('.period-option.active')?.getAttribute('data-period') || '24h';
                
                // 检查是否为PH或水位传感器
                const isSensorDevice = deviceData.id === 'ph' || deviceData.id === 'water';
                
                // 更新设备统计数据
                updateDeviceStatistics(deviceData);
                
                // 所有设备都显示布尔矩阵
                setTimeout(() => {
                    try {
                        // 更新状态矩阵 - 确保强制重新生成
                        const statusMatrix = document.getElementById('statusMatrix');
                        if (statusMatrix) {
                            statusMatrix.innerHTML = '';
                            console.log('已清空状态矩阵，准备重新生成');
                        }
                        
                        // 布尔历史部分始终显示
                        document.getElementById('booleanHistorySection').style.display = 'block';
                        
                        // 只初始化一次状态矩阵
                        initStatusMatrix(period);
                        
                        // 只有PH传感器和水位传感器显示图表和预测
                        if (isSensorDevice) {
                            document.getElementById('numericHistorySection').style.display = 'block';
                            document.getElementById('predictionSection').style.display = 'block';
                            
                            const chartType = document.querySelector('.toggle-option.active')?.getAttribute('data-view') || 'line';
                            
                            // 延迟加载图表
                            setTimeout(() => {
                                try {
                                    // 获取日期范围
                                    const endDate = new Date();
                                    const startDate = new Date(endDate.getTime() - 5 * 24 * 60 * 60 * 1000);
                                    initDataChartWithDateRange(startDate, endDate, chartType);
                                    initPredictionChartWithDateRange(startDate, endDate);
                                } catch (error) {
                                    console.error('初始化图表出错:', error);
                                }
                            }, 300);
                        } else {
                            // 对于泵设备，隐藏图表和预测部分
                            document.getElementById('numericHistorySection').style.display = 'none';
                            document.getElementById('predictionSection').style.display = 'none';
                        }
                    } catch (error) {
                        console.error('更改设备出错:', error);
                        // 尝试恢复状态矩阵
                        console.log('尝试恢复状态矩阵...');
                        setTimeout(() => {
                            const statusMatrix = document.getElementById('statusMatrix');
                            if (statusMatrix && statusMatrix.childElementCount === 0) {
                                initStatusMatrix(period);
                            }
                        }, 500);
                    }
                }, 300);
            } catch (error) {
                console.error('切换设备时出错:', error);
                // 强制重新初始化矩阵
                setTimeout(() => {
                    const period = '24h';
                    const statusMatrix = document.getElementById('statusMatrix');
                    if (statusMatrix && statusMatrix.childElementCount === 0) {
                        initStatusMatrix(period);
                    }
                }, 500);
            }
            
            // 添加设备选择器滚动指引
            addScrollGuide();
            
            // 更新设备类型提示和刷新按钮
            addDeviceTypeIndicator();
            addDeviceRefreshButton();
        }
        
        // 更新设备统计数据
        function updateDeviceStatistics(device) {
            // 获取统计卡片元素
            const statCards = document.querySelectorAll('.stat-card');
            if (!statCards || statCards.length < 3) return;
            
            // 根据设备类型设置不同的统计数据
            const isPumpDevice = !['ph', 'water'].includes(device.id);
            
            if (isPumpDevice) {
                // 泵设备的统计数据
                const runHours = (Math.random() * 10 + 10).toFixed(1);
                const faultFreq = (Math.random() * 4).toFixed(1);
                const effScore = Math.floor(Math.random() * 20 + 80);
                
                // 更新第一个统计卡片 - 平均运行时间
                const runTimeCard = statCards[0];
                if (runTimeCard) {
                    const statValue = runTimeCard.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = `${runHours}小时/天`;
                    }
                    const statLabel = runTimeCard.querySelector('.stat-label');
                    if (statLabel) {
                        statLabel.textContent = '平均运行时间';
                    }
                }
                
                // 更新第二个统计卡片 - 故障频率
                const faultCard = statCards[1];
                if (faultCard) {
                    const statValue = faultCard.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = `${faultFreq}次/月`;
                    }
                    const statLabel = faultCard.querySelector('.stat-label');
                    if (statLabel) {
                        statLabel.textContent = '故障频率';
                    }
                }
                
                // 更新第三个统计卡片 - 能效评分
                const scoreCard = statCards[2];
                if (scoreCard) {
                    const statValue = scoreCard.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = `${effScore}/100`;
                    }
                    const statLabel = scoreCard.querySelector('.stat-label');
                    if (statLabel) {
                        statLabel.textContent = '能效评分';
                    }
                }
            } else if (device.id === 'ph') {
                // PH传感器的统计数据
                const avgPh = (Math.random() * 1.5 + 6.5).toFixed(1);
                const stability = (Math.random() * 30 + 70).toFixed(0);
                const alarmRate = (Math.random() * 10).toFixed(1);
                
                // 更新第一个统计卡片 - 平均PH值
                const avgCard = statCards[0];
                if (avgCard) {
                    const statValue = avgCard.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = avgPh;
                    }
                    const statLabel = avgCard.querySelector('.stat-label');
                    if (statLabel) {
                        statLabel.textContent = '平均PH值';
                    }
                }
                
                // 更新第二个统计卡片 - 稳定性
                const stabilityCard = statCards[1];
                if (stabilityCard) {
                    const statValue = stabilityCard.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = `${stability}%`;
                    }
                    const statLabel = stabilityCard.querySelector('.stat-label');
                    if (statLabel) {
                        statLabel.textContent = '数据稳定性';
                    }
                }
                
                // 更新第三个统计卡片 - 异常率
                const alarmCard = statCards[2];
                if (alarmCard) {
                    const statValue = alarmCard.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = `${alarmRate}%`;
                    }
                    const statLabel = alarmCard.querySelector('.stat-label');
                    if (statLabel) {
                        statLabel.textContent = '异常率';
                    }
                }
            } else if (device.id === 'water') {
                // 水位传感器的统计数据
                const avgLevel = (Math.random() * 20 + 70).toFixed(1);
                const fluctuation = (Math.random() * 20 + 5).toFixed(1);
                const warningCount = Math.floor(Math.random() * 10);
                
                // 更新第一个统计卡片 - 平均水位
                const avgCard = statCards[0];
                if (avgCard) {
                    const statValue = avgCard.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = `${avgLevel}%`;
                    }
                    const statLabel = avgCard.querySelector('.stat-label');
                    if (statLabel) {
                        statLabel.textContent = '平均水位';
                    }
                }
                
                // 更新第二个统计卡片 - 波动范围
                const fluctuationCard = statCards[1];
                if (fluctuationCard) {
                    const statValue = fluctuationCard.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = `±${fluctuation}%`;
                    }
                    const statLabel = fluctuationCard.querySelector('.stat-label');
                    if (statLabel) {
                        statLabel.textContent = '波动范围';
                    }
                }
                
                // 更新第三个统计卡片 - 警告次数
                const warningCard = statCards[2];
                if (warningCard) {
                    const statValue = warningCard.querySelector('.stat-value');
                    if (statValue) {
                        statValue.textContent = `${warningCount}次`;
                    }
                    const statLabel = warningCard.querySelector('.stat-label');
                    if (statLabel) {
                        statLabel.textContent = '警告次数';
                    }
                }
            }
            
            // 添加动画效果
            statCards.forEach(card => {
                const value = card.querySelector('.stat-value');
                if (value) {
                    value.style.animation = 'none';
                    setTimeout(() => {
                        value.style.animation = 'pulse 1s ease-in-out';
                    }, 10);
                }
            });
        }
        
                    // 更新设备信息
            function updateDeviceInfo() {
                const deviceIconElement = document.getElementById('deviceIcon');
                const deviceNameElement = document.getElementById('deviceName');
                const deviceStatusElement = document.getElementById('deviceStatus');
                
                // 设置图标
                deviceIconElement.innerHTML = deviceIcons[deviceData.icon] || deviceIcons['gear'];
                deviceIconElement.className = `device-icon-large ${deviceData.icon}`;
                
                // 设置名称
                deviceNameElement.textContent = deviceData.name;
                
                // 设置状态
                deviceStatusElement.textContent = deviceData.status;
                if (deviceData.type === 'sensor') {
                    deviceStatusElement.className = 'device-status-large sensor-value';
                } else {
                    deviceStatusElement.className = deviceData.status === '运行中' ? 
                        'device-status-large running' : 'device-status-large stopped';
                }
                
                // 更新页面标题
                document.querySelector('.page-title').textContent = `${deviceData.name}历史数据`;
                
                // 更新设备选择器中的选择状态
                document.querySelectorAll('.device-option').forEach(option => {
                    const optionId = option.getAttribute('data-device-id');
                    if (optionId === deviceData.id) {
                        option.classList.add('active');
                    } else {
                        option.classList.remove('active');
                    }
                });
                
                // 检查是否为传感器设备，显示或隐藏相关部分
                updateSectionVisibility();
                
                // 强制显示PH和水位传感器的图表
                if (deviceData.id === 'ph' || deviceData.id === 'water') {
                    const numericHistorySection = document.getElementById('numericHistorySection');
                    const predictionSection = document.getElementById('predictionSection');
                    
                    if (numericHistorySection) numericHistorySection.style.display = 'block';
                    if (predictionSection) predictionSection.style.display = 'block';
                    
                    // 立即初始化图表，不要等待
                    const period = document.querySelector('.period-option.active')?.getAttribute('data-period') || '24h';
                    const chartType = document.querySelector('.toggle-option.active')?.getAttribute('data-view') || 'line';
                    
                    // 首先确保图表容器存在
                    const dataChartCanvas = document.getElementById('dataChart');
                    const predictionChartCanvas = document.getElementById('predictionChart');
                    
                    if (!dataChartCanvas || !predictionChartCanvas) {
                        console.error('图表Canvas元素不存在');
                        showToast('图表元素不存在，无法显示数据', 'error');
                        return;
                    }
                    
                    // 确保Chart.js库已加载
                    if (typeof Chart === 'undefined') {
                        console.error('Chart.js库未加载');
                        showToast('图表库未加载，请刷新页面重试', 'error');
                        return;
                    }
                    
                    try {
                        // 安全地初始化图表
                        console.log('开始初始化数据图表...');
                        // 确保图表容器可见
                        const chartContainer = dataChartCanvas.parentElement;
                        if (chartContainer) {
                            chartContainer.style.display = 'block';
                            chartContainer.style.visibility = 'visible';
                            chartContainer.style.height = '260px';
                        }
                        
                        // 强制清除可能存在的旧图表实例
                        if (window.dataChart && typeof window.dataChart.destroy === 'function') {
                            window.dataChart.destroy();
                            window.dataChart = null;
                        }
                        
                        // 获取日期范围
                        const endDate = new Date();
                        const startDate = new Date(endDate.getTime() - 5 * 24 * 60 * 60 * 1000);
                        
                        // 初始化数据图表
                        initDataChartWithDateRange(startDate, endDate, chartType);
                        
                        console.log('开始初始化预测图表...');
                        // 确保预测图表容器可见
                        const predictionContainer = predictionChartCanvas.parentElement;
                        if (predictionContainer) {
                            predictionContainer.style.display = 'block';
                            predictionContainer.style.visibility = 'visible';
                            predictionContainer.style.height = '260px';
                        }
                        
                        // 强制清除可能存在的旧预测图表实例
                        if (window.predictionChart && typeof window.predictionChart.destroy === 'function') {
                            window.predictionChart.destroy();
                            window.predictionChart = null;
                        }
                        
                        // 初始化预测图表
                        initPredictionChartWithDateRange(startDate, endDate);
                        console.log('传感器图表初始化完成');
                        
                    } catch (error) {
                        console.error('初始化传感器图表出错:', error);
                        showToast('图表初始化失败: ' + error.message, 'warning');
                        
                        // 再次尝试初始化，但使用更简单的方式
                        setTimeout(() => {
                            try {
                                // 创建简单的图表，不依赖外部数据
                                createSimpleChart(dataChartCanvas, '数据图表');
                                createSimpleChart(predictionChartCanvas, '预测图表');
                            } catch (e) {
                                console.error('创建简单图表失败:', e);
                            }
                        }, 500);
                    }
                }
                
                // 创建简单图表的辅助函数
                function createSimpleChart(canvas, title) {
                    if (!canvas || !canvas.getContext) return;
                    
                    const ctx = canvas.getContext('2d');
                    if (!ctx) return;
                    
                    // 销毁可能存在的图表
                    if (canvas.chart && typeof canvas.chart.destroy === 'function') {
                        canvas.chart.destroy();
                    }
                    
                    // 创建简单的图表
                    const labels = ['1', '2', '3', '4', '5', '6', '7'];
                    const data = {
                        labels: labels,
                        datasets: [{
                            label: deviceData.id === 'ph' ? 'PH值' : '水位 (%)',
                            data: [65, 59, 80, 81, 56, 55, 70],
                            backgroundColor: 'rgba(33, 150, 243, 0.2)',
                            borderColor: 'rgba(33, 150, 243, 1)',
                            borderWidth: 2
                        }]
                    };
                    
                    const config = {
                        type: 'line',
                        data: data,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                title: {
                                    display: true,
                                    text: title + ' (示例数据)'
                                }
                            }
                        }
                    };
                    
                    // 创建图表
                    canvas.chart = new Chart(ctx, config);
                }
            }
        
        // 更新数据历史和预测分析部分的可见性
        function updateSectionVisibility() {
            const isSensorDevice = deviceData.id === 'ph' || deviceData.id === 'water';
            const numericHistorySection = document.getElementById('numericHistorySection');
            const predictionSection = document.getElementById('predictionSection');
            
            if (numericHistorySection && predictionSection) {
                if (isSensorDevice) {
                    numericHistorySection.style.display = 'block';
                    predictionSection.style.display = 'block';
                } else {
                    numericHistorySection.style.display = 'none';
                    predictionSection.style.display = 'none';
                }
            }
        }
        
        // 初始化布尔型状态矩阵
        function initStatusMatrix(period = '24h') {
            console.log('正在初始化状态矩阵，时间周期:', period);
            
            // 强制确保状态矩阵容器存在
            const booleanSection = document.getElementById('booleanHistorySection');
            if (!booleanSection) {
                console.error('找不到布尔历史记录部分');
                return;
            }
            
            // 检查是否已存在状态矩阵，如果存在则移除它
            let existingMatrix = document.getElementById('statusMatrix');
            if (existingMatrix) {
                existingMatrix.remove();
            }
            
            // 创建新的状态矩阵
            const newMatrix = document.createElement('div');
            newMatrix.id = 'statusMatrix';
            newMatrix.className = 'boolean-matrix';
            
            // 查找section-header后面的位置
            const sectionHeader = booleanSection.querySelector('.section-header');
            if (sectionHeader) {
                booleanSection.insertBefore(newMatrix, sectionHeader.nextElementSibling);
            } else {
                booleanSection.appendChild(newMatrix);
            }
            
            const matrix = document.getElementById('statusMatrix');
            if (!matrix) {
                console.error('找不到状态矩阵元素');
                return;
            }
            
            // 清空矩阵内容
            matrix.innerHTML = '';
            console.log('矩阵内容已清空，准备重新生成');
            
            // 添加设备类型样式
            const isPumpDevice = !['ph', 'water'].includes(deviceData.id);
            matrix.className = isPumpDevice ? 'boolean-matrix device-pump' : 'boolean-matrix device-sensor';
            
            // 更新矩阵标题 - 设备特定标题
            const sectionTitle = document.querySelector('#booleanHistorySection .section-title');
            if (sectionTitle) {
                sectionTitle.textContent = `${deviceData.name}${isPumpDevice ? '运行状态历史' : '测量值状态历史'}`;
            }
            
            // 更新时间信息显示
            const timeInfo = document.querySelector('.time-info');
            if (timeInfo) {
                let periodText;
                switch(period) {
                    case '3d': periodText = '最近3天'; break;
                    case '5d': periodText = '最近5天'; break;
                    case '7d': periodText = '最近7天'; break;
                    case '15d': periodText = '最近15天'; break;
                    case '30d': periodText = '最近30天'; break;
                    default: periodText = '最近24小时'; // 24h
                }
                timeInfo.textContent = periodText;
            }
            
            // 根据不同时间周期配置矩阵
            let days = 1;
            let cellsPerDay = 24;
            let cellLabel = '';
            let totalCells = 24;
            let columnsCount = 8;
            
            // 配置不同时间周期的矩阵属性
            switch(period) {
                case '3d':
                    days = 3;
                    cellsPerDay = 8;
                    totalCells = days * cellsPerDay;
                    columnsCount = 8;
                    cellLabel = '时段';
                    break;
                case '5d':
                    days = 5;
                    cellsPerDay = 6; /* 每4小时一个时段 */
                    totalCells = days * cellsPerDay;
                    columnsCount = 6;
                    cellLabel = '时段';
                    break;
                case '7d':
                    days = 7;
                    cellsPerDay = 4;
                    totalCells = days * cellsPerDay;
                    columnsCount = 7;
                    cellLabel = '时段';
                    break;
                default: // 24h
                    days = 1;
                    cellsPerDay = 24;
                    totalCells = 24;
                    columnsCount = 8;
                    cellLabel = '时';
            }
            
            // 设置矩阵列数
            matrix.style.gridTemplateColumns = `repeat(${columnsCount}, 1fr)`;
            
            // 为矩阵容器添加加载指示器
            const matrixContainer = matrix.parentElement;
            if (matrixContainer) {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'matrix-loading';
                loadingDiv.style.position = 'absolute';
                loadingDiv.style.top = '0';
                loadingDiv.style.left = '0';
                loadingDiv.style.right = '0';
                loadingDiv.style.bottom = '0';
                loadingDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
                loadingDiv.style.display = 'flex';
                loadingDiv.style.justifyContent = 'center';
                loadingDiv.style.alignItems = 'center';
                loadingDiv.style.zIndex = '100';
                loadingDiv.style.flexDirection = 'column';
                
                const spinner = document.createElement('div');
                spinner.style.width = '30px';
                spinner.style.height = '30px';
                spinner.style.border = '3px solid rgba(0, 0, 0, 0.1)';
                spinner.style.borderTopColor = isPumpDevice ? '#4caf50' : '#2196f3';
                spinner.style.borderRadius = '50%';
                spinner.style.animation = 'spin 1s linear infinite';
                
                const loadingText = document.createElement('div');
                loadingText.style.marginTop = '10px';
                loadingText.style.fontSize = '14px';
                loadingText.style.color = '#666';
                loadingText.textContent = '正在加载历史数据...';
                
                loadingDiv.appendChild(spinner);
                loadingDiv.appendChild(loadingText);
                
                matrixContainer.style.position = 'relative';
                matrixContainer.appendChild(loadingDiv);
                
                try {
                    // 创建模拟数据以确保矩阵始终显示
                    // 这样即使API调用失败，我们也能看到矩阵格子
                    const deviceHistoryData = generateMockData(totalCells, deviceData.id);
                    
                    // 延迟一点以显示加载效果
                    setTimeout(() => {
                        // 移除加载指示器
                        if (loadingDiv.parentNode) {
                            loadingDiv.parentNode.removeChild(loadingDiv);
                        }
                        
                        // 清空矩阵内容，确保只创建一次单元格
                        if (matrix.childElementCount > 0) {
                            console.log(`矩阵已有${matrix.childElementCount}个子元素，清空后再创建`);
                            matrix.innerHTML = '';
                        }
                        
                        // 创建矩阵单元格
                        createMatrixCells(deviceHistoryData);
                        
                        // 尝试从API获取真实数据
                        fetchRealData(period, totalCells, isPumpDevice, matrix);
                    }, 800);
                    
                } catch (error) {
                    console.error('初始化矩阵出错:', error);
                    
                    // 移除加载指示器
                    if (loadingDiv.parentNode) {
                        loadingDiv.parentNode.removeChild(loadingDiv);
                    }
                    
                    // 创建空矩阵以确保UI不会破损
                    createMatrixCells(Array(totalCells).fill(null));
                    
                    // 显示错误提示
                    showToast('加载历史数据失败: ' + error.message);
                }
            }
            
            // 生成模拟数据
            function generateMockData(count, deviceId) {
                const data = [];
                
                for (let i = 0; i < count; i++) {
                    // 对于泵设备，生成运行/停止状态
                    if (['pump1', 'pump2', 'pump3', 'pump4', 'pump5', 'pump6'].includes(deviceId)) {
                        // 70%概率为运行状态
                        data[i] = Math.random() < 0.7;
                    } 
                    // 对于PH传感器，生成是否在正常范围内
                    else if (deviceId === 'ph') {
                        // 80%概率为正常范围
                        data[i] = Math.random() < 0.8;
                    }
                    // 对于水位传感器，生成是否在正常范围内
                    else if (deviceId === 'water') {
                        // 90%概率为正常范围
                        data[i] = Math.random() < 0.9;
                    }
                    else {
                        // 默认50%概率
                        data[i] = Math.random() < 0.5;
                    }
                    
                    // 减少无数据的概率，确保大部分单元格都有数据
                    if (Math.random() < 0.05) {
                        data[i] = null;
                    }
                }
                
                console.log(`生成了${count}个模拟数据点，设备ID: ${deviceId}`);
                return data;
            }
            
            // 尝试从API获取真实数据
            function fetchRealData(period, totalCells, isPumpDevice, matrix) {
                // 创建token
                try {
                    const params = {
                        author_key: '****************************************************************',
                        version: '2022-05-01',
                        user_id: '420568',
                    };
                    
                    if (typeof createCommonTokenFunc !== 'function') {
                        console.error('createCommonToken函数未找到');
                        return;
                    }
                    
                    const token = createCommonTokenFunc(params);
                    
                    // 确定时间范围
                    const now = new Date();
                    let start;
                    
                    switch(period) {
                        case '3d':
                            start = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
                            break;
                        case '5d':
                            start = new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000);
                            break;
                        case '7d':
                            start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                            break;
                        default: // 24h
                            start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    }
                    
                    // 根据设备类型确定功能点标识符
                    const identifier = getCorrectDatastreamId(deviceData.id);
                    
                    // 获取毫秒时间戳
                    const startTime = start.getTime();
                    const endTime = now.getTime();
                    
                    console.log(`获取设备历史数据: ${identifier} ${startTime} ${endTime}`);
                    
                    // 构建API请求参数 - 使用新的API格式
                    const queryParams = {
                        productId: 'HTJ98Pjh4a',
                        deviceName: 'flotation',
                        datastreamId: identifier, // 对应新API的identifier参数
                        start: startTime, // 毫秒时间戳
                        end: endTime,     // 毫秒时间戳
                        limit: 1000,      // 请求更多数据点以确保覆盖整个时间范围
                        accessKey: '****************************************************************'
                    };
                    
                    // 使用fetchHistoricalData函数获取历史数据
                    if (typeof window.fetchHistoricalData === 'function') {
                        // 使用已定义的变量identifier和时间戳
                        console.log(`获取设备历史数据: ${identifier} ${startTime} ${endTime}`);
                        
                        window.fetchHistoricalData(queryParams)
                            .then(data => {
                                if (data && data.code === 0) {
                                    console.log('获取到历史数据:', data);
                                    
                                    // 检查数据结构
                                    if (!data.data || !data.data.datastreams || !Array.isArray(data.data.datastreams) || data.data.datastreams.length === 0) {
                                        console.log('API返回的数据中没有datastreams或为空数组，尝试创建默认结构');
                                        
                                        // 创建默认数据结构
                                        if (!data.data) data.data = {};
                                        if (!data.data.datastreams) data.data.datastreams = [];
                                        
                                        // 添加空的datastream
                                        if (data.data.datastreams.length === 0) {
                                            data.data.datastreams.push({
                                                id: getCorrectDatastreamId(deviceData.id),
                                                datapoints: []
                                            });
                                        }
                                    }
                                    
                                    // 处理历史数据并更新矩阵
                                    updateMatrixWithRealData(data, matrix, isPumpDevice, totalCells, period);
                                } else {
                                    console.error('API返回错误:', data);
                                }
                            })
                            .catch(error => {
                                console.error('获取历史数据失败:', error);
                            });
                    } else {
                        console.warn('fetchHistoricalData函数不可用，无法获取真实数据');
                    }
                } catch (error) {
                    console.error('创建token失败:', error);
                }
            }
            
            // 使用真实数据更新矩阵
            function updateMatrixWithRealData(data, matrix, isPumpDevice, totalCells, period) {
                if (!data || !data.data || !data.data.datastreams || data.data.datastreams.length === 0) {
                    console.warn('没有获取到有效的历史数据');
                    return;
                }
                
                try {
                    const datastream = data.data.datastreams[0];
                    if (!datastream.datapoints) {
                        console.warn('datapoints字段不存在');
                        datastream.datapoints = [];
                    }
                    
                    const datapoints = datastream.datapoints;
                    console.log(`获取到${datapoints.length}个历史数据点`);
                    
                    // 如果没有数据点，仍然继续处理，只是所有单元格都会显示为无数据状态
                    
                    // 将数据点转换为矩阵格式
                    const now = new Date();
                    let timeRange;
                    
                    switch(period) {
                        case '3d': timeRange = 3 * 24 * 60 * 60 * 1000; break;
                        case '5d': timeRange = 5 * 24 * 60 * 60 * 1000; break;
                        case '7d': timeRange = 7 * 24 * 60 * 60 * 1000; break;
                        default: timeRange = 24 * 60 * 60 * 1000; // 24h
                    }
                    
                    const start = new Date(now.getTime() - timeRange);
                    
                    // 计算每个单元格代表的时间间隔
                    const timePerCell = timeRange / totalCells;
                    
                    // 创建时间段数组，每个单元格对应一个时间段
                    const timeSegments = Array(totalCells).fill().map((_, i) => {
                        const segmentStart = new Date(start.getTime() + i * timePerCell);
                        const segmentEnd = new Date(segmentStart.getTime() + timePerCell);
                        return { start: segmentStart, end: segmentEnd, hasData: false, value: null };
                    });
                    
                    // 将数据点分配到对应的时间段
                    datapoints.forEach(point => {
                        const pointTime = new Date(point.at);
                        const pointValue = point.value;
                        
                        // 找到对应的时间段
                        for (let i = 0; i < timeSegments.length; i++) {
                            const segment = timeSegments[i];
                            if (pointTime >= segment.start && pointTime < segment.end) {
                                segment.hasData = true;
                                
                                // 对于布尔型设备（泵），转换为布尔值
                                if (isPumpDevice) {
                                    segment.value = pointValue === '1' || pointValue === 'true' || pointValue === true;
                                } 
                                // 对于传感器，判断是否在正常范围内
                                else {
                                    const numValue = parseFloat(pointValue);
                                    if (deviceData.id === 'ph') {
                                        // PH值在6.5-7.5之间为正常
                                        segment.value = numValue >= 6.5 && numValue <= 7.5;
                                    } else if (deviceData.id === 'water') {
                                        // 水位在70%-90%之间为正常
                                        segment.value = numValue >= 70 && numValue <= 90;
                                    } else {
                                        segment.value = true; // 默认为正常
                                    }
                                }
                                
                                break;
                            }
                        }
                    });
                    
                    // 更新矩阵单元格
                    const cells = matrix.querySelectorAll('.matrix-cell');
                    
                    // 检查单元格数量是否是时间段数量的倍数
                    if (cells.length % timeSegments.length === 0) {
                        // 计算每个时间段对应的单元格数量
                        const cellsPerSegment = cells.length / timeSegments.length;
                        console.log(`每个时间段对应${cellsPerSegment}个单元格`);
                        
                        // 更新所有单元格
                        for (let i = 0; i < timeSegments.length; i++) {
                            const segment = timeSegments[i];
                            
                            // 更新该时间段对应的所有单元格
                            for (let j = 0; j < cellsPerSegment; j++) {
                                const cellIndex = i * cellsPerSegment + j;
                                if (cellIndex < cells.length) {
                                    const cell = cells[cellIndex];
                                    
                                    // 移除所有状态类
                                    cell.classList.remove('active', 'inactive', 'no-data');
                                    
                                    if (!segment.hasData) {
                                        cell.classList.add('no-data');
                                        cell.style.backgroundColor = 'transparent';
                                        cell.style.border = '1px solid #ddd';
                                    } else if (segment.value === true) {
                                        cell.classList.add('active');
                                        cell.style.backgroundColor = isPumpDevice ? '#4caf50' : '#2196f3';
                                        cell.style.color = 'white';
                                    } else {
                                        cell.classList.add('inactive');
                                        cell.style.backgroundColor = isPumpDevice ? '#f44336' : '#ff9800';
                                        cell.style.color = 'white';
                                    }
                                }
                            }
                        }
                        
                        console.log('矩阵已使用真实数据更新');
                    } else {
                        console.warn(`单元格数量(${cells.length})与时间段数量(${timeSegments.length})不匹配，无法整除`);
                        
                        // 尝试使用最小的单元格数量更新
                        const minCells = Math.min(cells.length, timeSegments.length);
                        console.log(`尝试更新前${minCells}个单元格`);
                        
                        for (let i = 0; i < minCells; i++) {
                            const cell = cells[i];
                            const segment = timeSegments[i];
                            
                            // 移除所有状态类
                            cell.classList.remove('active', 'inactive', 'no-data');
                            
                            if (!segment.hasData) {
                                cell.classList.add('no-data');
                                cell.style.backgroundColor = 'transparent';
                                cell.style.border = '1px solid #ddd';
                            } else if (segment.value === true) {
                                cell.classList.add('active');
                                cell.style.backgroundColor = isPumpDevice ? '#4caf50' : '#2196f3';
                                cell.style.color = 'white';
                            } else {
                                cell.classList.add('inactive');
                                cell.style.backgroundColor = isPumpDevice ? '#f44336' : '#ff9800';
                                cell.style.color = 'white';
                            }
                        }
                    }
                } catch (error) {
                    console.error('更新矩阵出错:', error);
                }
            }
            
            // 创建矩阵单元格
            function createMatrixCells(deviceData) {
                let currentDay = -1;
                const today = new Date();
                
                console.log(`开始创建${totalCells}个矩阵单元格，列数：${columnsCount}`);
                
                // 保持矩阵格式，不使用日期分隔指示器
                // 根据不同时间周期设置矩阵单元格
                for (let i = 0; i < totalCells; i++) {
                    // 移除日期分隔指示器，保持矩阵格式
                    
                    const cell = document.createElement('div');
                    cell.className = 'matrix-cell';
                    
                    // 确保deviceData存在且有足够的元素
                    const value = deviceData && i < deviceData.length ? deviceData[i] : null;
                    
                    // 如果没有数据，显示无数据状态 - 只有边框，不填充颜色
                    if (value === null) {
                        cell.classList.add('no-data');
                        cell.style.border = '1px solid #ddd';
                        cell.style.backgroundColor = 'transparent';
                    } else {
                        // 根据值设置单元格状态
                        if (value === true) {
                            cell.classList.add('active');
                            // 对于布尔型设备，true为绿色，false为红色
                            cell.style.backgroundColor = isPumpDevice ? '#4caf50' : '#2196f3';
                        } else {
                            cell.classList.add('inactive');
                            cell.style.backgroundColor = isPumpDevice ? '#f44336' : '#ff9800';
                        }
                        cell.style.color = 'white';
                    }
                    
                    // 设置时间标签
                    if (period === '24h') {
                        cell.textContent = `${i}${cellLabel}`;
                    } else {
                        // 其他视图显示时段号
                        const hourIndex = i % cellsPerDay;
                        cell.textContent = `${hourIndex + 1}${cellLabel}`;
                    }
                    
                    // 添加鼠标悬停效果
                    addCellHoverEffects(cell, i, value, today);
                    
                    // 添加到矩阵
                    matrix.appendChild(cell);
                }
                
                console.log(`矩阵单元格创建完成，总共${matrix.childElementCount}个元素`);
                
                // 确保矩阵可见
                matrix.style.display = 'grid';
                matrix.style.opacity = '1';
                
                // 更新图例
                updateMatrixLegend(isPumpDevice);
            }
            
            // 更新矩阵图例
            function updateMatrixLegend(isPumpDevice) {
                console.log('更新矩阵图例');
                
                // 检查是否存在图例容器，如果不存在则创建
                let legendContainer = document.querySelector('.matrix-legend');
                if (!legendContainer) {
                    console.log('图例容器不存在，创建新容器');
                    legendContainer = document.createElement('div');
                    legendContainer.className = 'matrix-legend';
                    
                    // 找到矩阵容器并添加图例
                    const matrixContainer = document.getElementById('statusMatrix');
                    if (matrixContainer && matrixContainer.parentElement) {
                        matrixContainer.parentElement.appendChild(legendContainer);
                    } else {
                        // 如果找不到矩阵容器，添加到布尔历史部分
                        const booleanSection = document.getElementById('booleanHistorySection');
                        if (booleanSection) {
                            booleanSection.appendChild(legendContainer);
                        } else {
                            console.error('无法找到合适的容器添加图例');
                            return;
                        }
                    }
                }
                
                // 清空现有图例
                legendContainer.innerHTML = '';
                
                // 创建运行/停止图例
                const activeItem = document.createElement('div');
                activeItem.className = 'legend-item';
                
                const activeColor = document.createElement('div');
                activeColor.className = 'legend-color active';
                activeColor.style.backgroundColor = isPumpDevice ? '#4caf50' : '#2196f3';
                
                const activeText = document.createElement('div');
                activeText.className = 'legend-text';
                activeText.textContent = isPumpDevice ? '运行' : '正常范围';
                
                activeItem.appendChild(activeColor);
                activeItem.appendChild(activeText);
                
                const inactiveItem = document.createElement('div');
                inactiveItem.className = 'legend-item';
                
                const inactiveColor = document.createElement('div');
                inactiveColor.className = 'legend-color inactive';
                inactiveColor.style.backgroundColor = isPumpDevice ? '#f44336' : '#ff9800';
                
                const inactiveText = document.createElement('div');
                inactiveText.className = 'legend-text';
                inactiveText.textContent = isPumpDevice ? '停止' : '异常范围';
                
                inactiveItem.appendChild(inactiveColor);
                inactiveItem.appendChild(inactiveText);
                
                // 添加无数据图例
                const noDataItem = document.createElement('div');
                noDataItem.className = 'legend-item';
                
                const noDataColor = document.createElement('div');
                noDataColor.className = 'legend-color';
                noDataColor.style.backgroundColor = 'transparent';
                noDataColor.style.border = '1px solid #ddd';
                
                const noDataText = document.createElement('div');
                noDataText.className = 'legend-text';
                noDataText.textContent = '无数据';
                
                noDataItem.appendChild(noDataColor);
                noDataItem.appendChild(noDataText);
                
                // 添加到图例容器
                legendContainer.appendChild(activeItem);
                legendContainer.appendChild(inactiveItem);
                legendContainer.appendChild(noDataItem);
                
                console.log('图例更新完成');
            }
            
            // 添加单元格悬停效果
            function addCellHoverEffects(cell, index, isActive, today) {
                cell.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                    this.style.zIndex = '5';
                    
                    // 为所有设备添加详细信息
                    let tooltipText = '';
                    const date = new Date(today);
                    
                    if (period !== '24h') {
                        // 计算日期
                        const dayIndex = Math.floor(index / cellsPerDay);
                        date.setDate(today.getDate() - (days - dayIndex - 1));
                    }
                    
                    const formattedDate = date.toLocaleDateString('zh-CN', {month: 'long', day: 'numeric'});
                    
                    if (this.classList.contains('no-data')) {
                        // 无数据单元格的提示
                        let timeInfo;
                        if (period === '24h') {
                            timeInfo = `${index}:00 - ${index+1}:00`;
                        } else if (period === '30d') {
                            timeInfo = formattedDate;
                        } else {
                            const hourIndex = index % cellsPerDay;
                            const hoursPerPeriod = 24 / cellsPerDay;
                            const startHour = hourIndex * hoursPerPeriod;
                            const endHour = startHour + hoursPerPeriod;
                            timeInfo = `${formattedDate} ${startHour}:00 - ${endHour}:00`;
                        }
                        tooltipText = `无数据: ${timeInfo}`;
                    } else if (isPumpDevice) {
                        // 泵设备显示运行状态
                        const status = this.classList.contains('active');
                        let timeInfo;
                        
                        if (period === '24h') {
                            timeInfo = `${index}:00 - ${index+1}:00`;
                        } else if (period === '30d') {
                            timeInfo = formattedDate;
                        } else {
                            const hourIndex = index % cellsPerDay;
                            const hoursPerPeriod = 24 / cellsPerDay;
                            const startHour = hourIndex * hoursPerPeriod;
                            const endHour = startHour + hoursPerPeriod;
                            timeInfo = `${formattedDate} ${startHour}:00 - ${endHour}:00`;
                        }
                        
                        tooltipText = status ? 
                            `运行: ${timeInfo}` : 
                            `停止: ${timeInfo}`;
                    } else if (deviceData.id === 'ph') {
                        // PH传感器显示值范围
                        const status = this.classList.contains('active');
                        let timeInfo;
                        
                        if (period === '24h') {
                            timeInfo = `${index}:00 - ${index+1}:00`;
                        } else if (period === '30d') {
                            timeInfo = formattedDate;
                        } else {
                            const hourIndex = index % cellsPerDay;
                            const hoursPerPeriod = 24 / cellsPerDay;
                            const startHour = hourIndex * hoursPerPeriod;
                            const endHour = startHour + hoursPerPeriod;
                            timeInfo = `${formattedDate} ${startHour}:00 - ${endHour}:00`;
                        }
                        
                        tooltipText = status ? 
                            `正常范围 (6.5-7.5): ${timeInfo}` : 
                            `异常范围: ${timeInfo}`;
                    } else if (deviceData.id === 'water') {
                        // 水位传感器显示值范围
                        const status = this.classList.contains('active');
                        let timeInfo;
                        
                        if (period === '24h') {
                            timeInfo = `${index}:00 - ${index+1}:00`;
                        } else if (period === '30d') {
                            timeInfo = formattedDate;
                        } else {
                            const hourIndex = index % cellsPerDay;
                            const hoursPerPeriod = 24 / cellsPerDay;
                            const startHour = hourIndex * hoursPerPeriod;
                            const endHour = startHour + hoursPerPeriod;
                            timeInfo = `${formattedDate} ${startHour}:00 - ${endHour}:00`;
                        }
                        
                        tooltipText = status ? 
                            `正常范围 (70%-90%): ${timeInfo}` : 
                            `异常范围: ${timeInfo}`;
                    }
                    
                    // 显示提示
                    showTooltip(tooltipText, this);
                });
                
                cell.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.zIndex = '';
                    
                    // 隐藏提示
                    hideTooltip();
                });
            }
            
            // 创建日期分隔指示器的帮助函数
            function addDayIndicator(dayIndex, today) {
                // 计算显示日期
                const displayDate = new Date(today);
                displayDate.setDate(today.getDate() - (days - dayIndex - 1));
                
                // 创建日期指示器
                const dayIndicator = document.createElement('div');
                dayIndicator.className = 'day-indicator';
                dayIndicator.textContent = displayDate.toLocaleDateString('zh-CN', {month: 'short', day: 'numeric'});
                dayIndicator.style.gridColumn = '1 / -1';
                return dayIndicator;
            }
            
            // 显示提示的函数
            function showTooltip(text, element) {
                // 移除现有提示
                hideTooltip();
                
                const tooltip = document.createElement('div');
                tooltip.className = 'cell-tooltip';
                tooltip.style.position = 'fixed';
                tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                tooltip.style.color = 'white';
                tooltip.style.padding = '8px 12px';
                tooltip.style.borderRadius = '4px';
                tooltip.style.fontSize = '12px';
                tooltip.style.zIndex = '100';
                tooltip.style.maxWidth = '300px';
                tooltip.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                tooltip.style.whiteSpace = 'nowrap';
                tooltip.textContent = text;
                
                document.body.appendChild(tooltip);
                
                // 获取元素位置
                const rect = element.getBoundingClientRect();
                
                // 定位提示
                tooltip.style.top = `${rect.top - 30}px`;
                tooltip.style.left = `${rect.left + rect.width / 2}px`;
                tooltip.style.transform = 'translateX(-50%)';
                
                // 确保提示不超出屏幕
                setTimeout(() => {
                    const tooltipRect = tooltip.getBoundingClientRect();
                    if (tooltipRect.left < 0) {
                        tooltip.style.left = '5px';
                        tooltip.style.transform = 'translateX(0)';
                    }
                    if (tooltipRect.right > window.innerWidth) {
                        tooltip.style.left = 'auto';
                        tooltip.style.right = '5px';
                        tooltip.style.transform = 'translateX(0)';
                    }
                }, 0);
            }
            
            // 隐藏提示的函数
            function hideTooltip() {
                const tooltips = document.querySelectorAll('.cell-tooltip');
                tooltips.forEach(t => t.parentNode.removeChild(t));
            }
        }
        
        // 使用自定义时间范围初始化数据图表
        function initDataChartWithDateRange(startDate, endDate, chartType = 'line') {
            console.log('初始化数据图表:', chartType, '开始:', startDate.toLocaleString(), '结束:', endDate.toLocaleString());
            
            const canvas = document.getElementById('dataChart');
            if (!canvas) {
                console.error('找不到图表Canvas元素');
                return;
            }
            
            // 确保Canvas元素可见
            canvas.style.display = 'block';
            
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('无法获取图表上下文');
                return;
            }
            
            // 销毁现有图表
            if (window.dataChart && typeof window.dataChart.destroy === 'function') {
                window.dataChart.destroy();
                window.dataChart = null;
            }
            
            // 使用传入的时间范围参数
            
            // 为图表容器添加加载指示器
            const chartContainer = canvas.parentElement;
            if (chartContainer) {
                const loadingSpinner = document.createElement('div');
                loadingSpinner.className = 'chart-loading-spinner';
                loadingSpinner.style.position = 'absolute';
                loadingSpinner.style.top = '50%';
                loadingSpinner.style.left = '50%';
                loadingSpinner.style.transform = 'translate(-50%, -50%)';
                loadingSpinner.style.width = '40px';
                loadingSpinner.style.height = '40px';
                loadingSpinner.style.border = '3px solid rgba(0, 0, 0, 0.1)';
                loadingSpinner.style.borderTopColor = '#2196f3';
                loadingSpinner.style.borderRadius = '50%';
                loadingSpinner.style.animation = 'spin 1s linear infinite';
                loadingSpinner.style.zIndex = '10';
                
                chartContainer.style.position = 'relative';
                chartContainer.appendChild(loadingSpinner);
                
                // 创建token
                const params = {
                    author_key: '****************************************************************',
                    version: '2022-05-01',
                    user_id: '420568',
                };

                let token;
                try {
                    if (typeof createCommonTokenFunc !== 'function') {
                        console.error('createCommonToken函数未找到');
                        showChartError(chartContainer, loadingSpinner, '无法创建授权Token');
                        return;
                    }
                    token = createCommonTokenFunc(params);
                } catch (error) {
                    console.error('创建token失败:', error);
                    showChartError(chartContainer, loadingSpinner, 'Token创建失败');
                    return;
                }
                
                // 格式化日期为API需要的格式 YYYY-MM-DDThh:mm:ss
                const formatDate = (date) => {
                    return date.toISOString().split('.')[0];
                };
                
                // 根据设备类型确定数据流ID
                let datastream_id;
                if (deviceData.id === 'ph') {
                    datastream_id = 'PH';
                } else if (deviceData.id === 'water') {
                    datastream_id = 'Water';
                } else {
                    console.error('不支持的设备类型:', deviceData.id);
                    showChartError(chartContainer, loadingSpinner, '不支持的设备类型');
                    return;
                }
                
                // 严格按照API文档构建请求参数
                const queryParams = {
                    productId: 'HTJ98Pjh4a',               // 对应API中的product_id
                    deviceName: 'flotation',               // 对应API中的device_name
                    datastreamId: datastream_id,           // 对应API中的identifier
                    start: startDate.getTime(),            // 用户选择的开始时间，毫秒时间戳
                    end: endDate.getTime(),                // 用户选择的结束时间，毫秒时间戳
                    limit: 1000,                           // 请求更多数据点以确保覆盖整个时间范围
                    sort: '1',                             // 1-正序，确保数据按时间先后排序
                    accessKey: '****************************************************************'
                };
                
                console.log(`获取设备历史数据: ${datastream_id}, 开始: ${startDate.toLocaleString()}, 结束: ${endDate.toLocaleString()}`);
                
                // 使用window上的fetchHistoricalData函数
                let dataPromise;
                
                if (typeof window.fetchHistoricalData === 'function') {
                    dataPromise = window.fetchHistoricalData(queryParams);
                } else {
                    // 如果函数不存在，则回退到原始的fetch方法
                const url = new URL('https://iot-api.heclouds.com/thingmodel/query-device-property-history');
                url.searchParams.append('product_id', 'HTJ98Pjh4a');
                url.searchParams.append('device_name', 'flotation');
                url.searchParams.append('identifier', datastream_id); // 使用identifier参数
                    url.searchParams.append('start_time', String(startDate.getTime()));
                    url.searchParams.append('end_time', String(endDate.getTime()));
                url.searchParams.append('limit', '1000');
                    url.searchParams.append('sort', '1');
                
                    dataPromise = fetch(url, {
                    method: 'GET',
                    headers: {
                        'authorization': token
                    }
                    });
                }
                
                dataPromise
                .then(response => {
                    // 处理fetchHistoricalData返回的数据（已经是JSON）
                    if (typeof window.fetchHistoricalData === 'function' && typeof response === 'object') {
                        return response; // 如果是对象，说明已经是JSON格式了
                    }
                    
                    // 处理标准fetch返回的Response对象
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(responseData => {
                    // 移除加载指示器
                    if (loadingSpinner.parentNode) {
                        loadingSpinner.parentNode.removeChild(loadingSpinner);
                    }
                    
                    // 处理API返回的数据
                    let chartData = {
                        labels: [],
                        values: []
                    };
                    
                    // 创建时间点（无论是否有数据都会用到）
                    // 修复now未定义的问题和period未定义的问题
                    const nowTime = new Date();
                    // 直接使用传入的startDate和endDate参数替代period计算
                    const timePoints = generateTimePointsBetween(startDate, endDate, 24); // 生成24个数据点
                    
                    let hasData = false;
                    
                    // 处理新API格式的响应
                    if (responseData && responseData.code === 0 && 
                        responseData.data && responseData.data.list) {
                        
                        const datapoints = responseData.data.list || [];
                        console.log(`获取到${datapoints.length}个数据点，时间范围: ${startDate.toLocaleString()} - ${endDate.toLocaleString()}`);
                        
                        // 调试输出每个数据点的值
                        if (datapoints.length > 0) {
                            datapoints.forEach((point, index) => {
                                console.log(`数据点[${index}]: 时间=${new Date(parseInt(point.time)).toLocaleString()}, 值=${point.value}`);
                            });
                        }
                        
                        if (datapoints.length > 0) {
                        // 确保数据点按时间排序
                        datapoints.sort((a, b) => parseInt(a.time) - parseInt(b.time));
                        
                            // 提取图表数据，确保值被正确转换为数字
                        chartData.labels = datapoints.map(point => new Date(parseInt(point.time)));
                            chartData.values = datapoints.map(point => {
                                // 处理字符串值
                                const numValue = Number(point.value);
                                console.log(`转换值: ${point.value} → ${numValue}`);
                                return isNaN(numValue) ? 0 : numValue;
                            });
                            
                            hasData = true;
                            console.log(`成功处理${datapoints.length}个数据点，开始创建图表，值范围: [${Math.min(...chartData.values)}, ${Math.max(...chartData.values)}]`);
                        } else {
                            console.log('数据列表为空，创建零值数据点');
                            // 使用预生成的时间点，值全部为0
                            chartData.labels = timePoints;
                            chartData.values = timePoints.map(() => 0);
                            // 标记为有数据，以便显示为0而不是空白
                            hasData = true;
                        }
                    } 
                    // 兼容旧API格式
                    else if (responseData && responseData.code === 0 && 
                        responseData.data && responseData.data.datastreams && 
                        responseData.data.datastreams.length > 0 && 
                        responseData.data.datastreams[0].datapoints) {
                        
                        const datapoints = responseData.data.datastreams[0].datapoints || [];
                        console.log(`获取到${datapoints.length}个数据点(旧API格式)`);
                        
                        if (datapoints.length > 0) {
                        // 确保数据点按时间排序
                        datapoints.sort((a, b) => new Date(a.at) - new Date(b.at));
                        
                        // 提取图表数据
                        chartData.labels = datapoints.map(point => new Date(point.at));
                        chartData.values = datapoints.map(point => parseFloat(point.value));
                        
                            hasData = true;
                            console.log(`成功处理${datapoints.length}个数据点，开始创建图表`);
                        } else {
                            console.log('数据点列表为空，创建零值数据点');
                            // 使用预生成的时间点，值全部为0
                            chartData.labels = timePoints;
                            chartData.values = timePoints.map(() => 0);
                            // 标记为有数据，以便显示为0而不是空白
                            hasData = true;
                        }
                    } 
                    // 处理API错误或无效响应的情况
                    else {
                        console.log('API响应无效或有错误，创建零值数据点');
                        // 使用预生成的时间点，值全部为0
                        chartData.labels = timePoints;
                        chartData.values = timePoints.map(() => 0);
                        // 标记为有数据，以便显示为0而不是空白
                        hasData = true;
                    }
                    
                    // 检查是否确实有数据
                    if (hasData) {
                        console.log(`数据范围: ${chartData.labels[0]} 到 ${chartData.labels[chartData.labels.length - 1]}`);
                        console.log(`值范围: ${Math.min(...chartData.values)} 到 ${Math.max(...chartData.values)}`);
                    } else {
                        console.warn(`在所选时间范围内没有获取到任何数据点`);
                    }
                    
                    // 如果没有数据，创建默认数据点并标记异常
                    if (!hasData) {
                        // 创建对应时间周期的时间点
                        const timePoints = generateTimePoints(start, now, period);
                        chartData.labels = timePoints;
                        
                        // 根据设备类型设置不同的默认值，使图表更加直观
                        if (deviceData.id === 'ph') {
                            // PH默认值设为7.0（中性）
                            chartData.values = timePoints.map(() => 7.0);
                        } else if (deviceData.id === 'water') {
                            // 水位默认值设为50%
                            chartData.values = timePoints.map(() => 50.0);
                        } else {
                            // 其他设备默认为0
                        chartData.values = timePoints.map(() => 0);
                        }
                        
                        console.log(`为${period}时间周期创建了${timePoints.length}个默认数据点`);
                    }
                    
                    // 创建图表
                    createChart(ctx, chartData, chartType, hasData);
                    
                    // 更新数据统计
                    updateDataStatistics(chartData.values, hasData);
                    
                    // 记录数据处理完成
                    console.log(`图表创建完成，类型: ${chartType}, 数据点数: ${chartData.values.length}`);
                })
                .catch(error => {
                    console.error('获取历史数据失败:', error);
                    
                    // 移除加载指示器
                    if (loadingSpinner.parentNode) {
                        loadingSpinner.parentNode.removeChild(loadingSpinner);
                    }
                    
                    // 根据时间周期创建适当数量的时间点
                    // 修复now未定义的问题和period未定义的问题
                    const nowTime = new Date();
                    // 直接使用传入的startDate和endDate参数替代period计算
                    const timePoints = generateTimePointsBetween(startDate, endDate, 24); // 生成24个数据点
                    
                    // 创建值为0的数据点，便于显示
                    let zeroValues;
                    if (deviceData.id === 'ph') {
                        // PH值显示为0
                        zeroValues = timePoints.map(() => 0);
                    } else if (deviceData.id === 'water') {
                        // 水位显示为0%
                        zeroValues = timePoints.map(() => 0);
                    } else {
                        // 其他设备显示为0
                        zeroValues = timePoints.map(() => 0);
                    }
                    
                    const chartData = {
                        labels: timePoints,
                        values: zeroValues
                    };
                    
                    // 记录错误
                    console.log('API错误，显示空图表');
                    
                    // 创建图表，标记为无数据
                    createChart(ctx, chartData, chartType, false);
                    
                    // 更新数据统计为空
                    try {
                        // 尝试更新统计数据
                        if (typeof updateDataStatistics === 'function') {
                    updateDataStatistics([], false);
                        }
                    } catch (err) {
                        console.error('更新统计数据失败:', err);
                    }
                    
                    // 显示错误提示
                    showToast('获取数据失败: ' + error.message, 'error');
                });
                
                // 根据时间周期获取毫秒数
                function getTimeRangeMilliseconds(period) {
                    switch(period) {
                        case '3d': return 3 * 24 * 60 * 60 * 1000;
                        case '5d': return 5 * 24 * 60 * 60 * 1000;
                        case '7d': return 7 * 24 * 60 * 60 * 1000;
                        case '15d': return 15 * 24 * 60 * 60 * 1000;
                        case '30d': return 30 * 24 * 60 * 60 * 1000;
                        default: return 24 * 60 * 60 * 1000; // 24h
                    }
                }
                
                // 生成时间点
                function generateTimePoints(start, end, period) {
                    const points = [];
                    const duration = end.getTime() - start.getTime();
                    
                    // 根据不同周期生成不同数量的点
                    let pointsCount;
                    switch(period) {
                        case '3d': pointsCount = 24; break;
                        case '5d': pointsCount = 30; break;
                        case '7d': pointsCount = 28; break;
                        case '15d': pointsCount = 30; break;
                        case '30d': pointsCount = 30; break;
                        default: pointsCount = 24; // 24h
                    }
                    
                    const interval = duration / (pointsCount - 1);
                    
                    for (let i = 0; i < pointsCount; i++) {
                        points.push(new Date(start.getTime() + i * interval));
                    }
                    
                    return points;
                }
                
                // 在两个日期之间生成均匀分布的时间点
                function generateTimePointsBetween(startDate, endDate, pointsCount) {
                    const points = [];
                    const duration = endDate.getTime() - startDate.getTime();
                    
                    // 确保至少有2个点
                    pointsCount = Math.max(2, pointsCount);
                    
                    // 计算时间间隔
                    const interval = duration / (pointsCount - 1);
                    
                    // 生成均匀分布的时间点
                    for (let i = 0; i < pointsCount; i++) {
                        points.push(new Date(startDate.getTime() + i * interval));
                    }
                    
                    return points;
                }
                
                // 创建图表
                function createChart(ctx, chartData, chartType, hasData) {
                    // 销毁现有图表
                    if (window.dataChart && typeof window.dataChart.destroy === 'function') {
                        window.dataChart.destroy();
                        window.dataChart = null;
                    }
                    
                    // 确保数据正确
                    if (!chartData.labels || !chartData.values) {
                        console.error('图表数据无效:', chartData);
                        chartData = { labels: [], values: [] };
                    }
                    
                    // 检查并记录数据状态
                    console.log(`创建图表 - 类型: ${chartType}, 数据点数: ${chartData.labels.length}, 有效数据: ${hasData}`);
                    
                    // 确保有足够的数据点以显示图表
                    if (chartData.labels.length < 2 || chartData.values.length < 2) {
                        console.log('数据点不足，创建默认数据点以确保图表可见');
                        const now = new Date();
                        const defaultLabels = [
                            new Date(now.getTime() - 24 * 60 * 60 * 1000),
                            now
                        ];
                        
                        // 根据设备类型设置合理默认值
                        const defaultValues = deviceData.id === 'ph' ? [7.0, 7.0] : [50.0, 50.0];
                        
                        // 合并现有数据和默认数据
                        if (chartData.labels.length === 1 && chartData.values.length === 1) {
                            // 如果只有一个数据点，添加另一个
                            chartData.labels.push(defaultLabels[1]);
                            chartData.values.push(chartData.values[0]);
                        } else {
                            // 没有数据点，使用默认值
                            chartData.labels = defaultLabels;
                            chartData.values = defaultValues;
                        }
                        
                        hasData = true; // 强制显示为有数据
                        console.log(`创建了默认数据点: ${chartData.labels.length}个`);
                    }
                    
                    // 定义设备特定颜色方案
                    const colorScheme = deviceData.id === 'ph' ? 
                        {
                            primary: '#5E35B1', // 紫色
                            secondary: '#7E57C2',
                            gradient: ['rgba(94, 53, 177, 0.8)', 'rgba(94, 53, 177, 0.1)'],
                            point: '#5E35B1',
                            hover: '#4527A0'
                        } : 
                        {
                            primary: '#0288D1', // 蓝色
                            secondary: '#29B6F6',
                            gradient: ['rgba(2, 136, 209, 0.8)', 'rgba(2, 136, 209, 0.1)'],
                            point: '#0288D1',
                            hover: '#01579B'
                        };
                    
                    // 为柱状图创建渐变色
                    let gradient;
                    if (chartType === 'bar' && ctx) {
                        // 创建自上而下的渐变
                        gradient = ctx.createLinearGradient(0, 0, 0, 400);
                        gradient.addColorStop(0, colorScheme.primary);
                        gradient.addColorStop(0.6, colorScheme.secondary);
                        gradient.addColorStop(1, 'rgba(' + hexToRgb(colorScheme.secondary) + ', 0.5)');
                    }
                    
                    // 格式化数据，确保x和y值正确，并过滤连续的零值点
                    let formattedData = []; // 改为let因为后面会修改length属性
                    let filteredData = []; // 将const改为let以允许重新赋值
                    
                    // 打印原始数据，便于调试
                    console.log('原始图表数据:');
                    console.log('标签:', chartData.labels);
                    console.log('值:', chartData.values);
                    
                    // 首先创建所有数据点
                    for (let i = 0; i < chartData.labels.length; i++) {
                        if (chartData.labels[i] !== undefined) {
                            // 确保值是数字，处理null、undefined或NaN为0
                            let value = chartData.values[i];
                            // 强制转换为数字
                            if (value === null || value === undefined) {
                                value = 0;
                            } else if (typeof value === 'string') {
                                value = Number(value);
                                if (isNaN(value)) value = 0;
                            }
                            
                            // 创建数据点
                            const dataPoint = {
                                x: chartData.labels[i],
                                y: value
                            };
                            
                            // 记录数据点
                            console.log(`数据点[${i}]: x=${dataPoint.x.toLocaleString()}, y=${dataPoint.y}`);
                            formattedData.push(dataPoint);
                        }
                    }
                    
                    // 优化绘图数据 - 先过滤0值，再进行均匀采样
                    // 确定采样目标数量 - 根据图表类型不同设置不同的目标点数
                    const targetPoints = chartType === 'line' ? 30 : 24;
                    
                    if (formattedData.length > 0) {
                        // 第一步：首先过滤掉大部分为0的数据点，但保留首尾点和趋势转折点
                        const nonZeroData = [];
                        
                        // 总是保留首尾点
                        nonZeroData.push(formattedData[0]);
                        
                        // 筛选中间的非零点和重要趋势点
                        for (let i = 1; i < formattedData.length - 1; i++) {
                            const point = formattedData[i];
                            const prevPoint = formattedData[i-1];
                            const nextPoint = formattedData[i+1];
                            
                            // 如果当前点不为0，或者是趋势转折点，则保留
                            if (point.y !== 0 || 
                                (prevPoint.y === 0 && nextPoint.y !== 0) || // 从零变为非零
                                (prevPoint.y !== 0 && nextPoint.y === 0) || // 从非零变为零
                                (point.y === 0 && i % Math.max(1, Math.floor(formattedData.length / 50)) === 0)) { // 保留部分零点以保持时间连续性
                                nonZeroData.push(point);
                            }
                        }
                        
                        // 确保添加最后一个点
                        if (formattedData.length > 1) {
                            nonZeroData.push(formattedData[formattedData.length - 1]);
                        }
                        
                        console.log(`过滤零值后的数据点数: ${nonZeroData.length}`);
                        
                        // 第二步：根据目标点数进行均匀采样
                        // 如果过滤后的数据点仍然很多，则进行均匀采样
                        if (nonZeroData.length > targetPoints) {
                            // 添加第一个点
                            filteredData.push(nonZeroData[0]);
                            
                            // 计算采样步长
                            const step = Math.max(1, Math.floor((nonZeroData.length - 2) / (targetPoints - 2)));
                            
                            // 对中间点进行均匀采样
                            for (let i = step; i < nonZeroData.length - 1; i += step) {
                                filteredData.push(nonZeroData[i]);
                            }
                            
                            // 确保添加最后一个点
                            if (filteredData[filteredData.length - 1] !== nonZeroData[nonZeroData.length - 1]) {
                                filteredData.push(nonZeroData[nonZeroData.length - 1]);
                            }
                        } 
                        // 如果过滤后的数据点数量适中，全部使用
                        else {
                            filteredData = [...nonZeroData];
                        }
                    }
                    
                    // 使用过滤后的数据
                    const optimizedData = filteredData.length > 2 ? filteredData : formattedData;
                    
                    // 确保至少有两个数据点用于绘图
                    if (formattedData.length < 2 && timePoints && timePoints.length >= 2) {
                        formattedData.length = 0; // 清空
                        formattedData.push({ x: timePoints[0], y: 0 });
                        formattedData.push({ x: timePoints[timePoints.length-1], y: 0 });
                        console.log('数据点不足，使用零值数据点确保图表可见');
                    }
                    
                    console.log(`图表格式化后的数据点数: ${formattedData.length}`);
                    
                    // 设置图表配置
                    console.log(`创建图表配置，类型: ${chartType}，数据点数: ${formattedData.length}`);
                    
                    // 调试信息：检查数据点内容
                    if (formattedData.length > 0) {
                        console.log(`第一个数据点: x=${formattedData[0].x.toLocaleString()}, y=${formattedData[0].y}`);
                        console.log(`最后一个数据点: x=${formattedData[formattedData.length-1].x.toLocaleString()}, y=${formattedData[formattedData.length-1].y}`);
                    }
                    
                    const chartConfig = {
                        type: chartType,
                        data: {
                            // 不需要额外的labels，因为我们使用x/y格式的数据点
                            labels: [], 
                            datasets: [{
                                label: deviceData.id === 'ph' ? 'PH值' : '水位 (%)',
                                data: optimizedData,
                                backgroundColor: chartType === 'bar' ? 
                                    (gradient || colorScheme.primary) : 
                                    'rgba(' + hexToRgb(colorScheme.primary) + ', 0.2)',
                                borderColor: colorScheme.primary,
                                borderWidth: chartType === 'line' ? 2.5 : 2,
                                pointRadius: chartType === 'line' ? 4 : 0,
                                barPercentage: 0.8, // 控制柱状图的宽度
                                categoryPercentage: 0.9, // 控制类别间距
                                pointBackgroundColor: colorScheme.point,
                                pointBorderColor: '#fff',
                                pointBorderWidth: 2,
                                pointHoverRadius: 6,
                                pointHoverBackgroundColor: colorScheme.hover,
                                pointHoverBorderColor: 'rgba(255, 255, 255, 0.9)',
                                pointHoverBorderWidth: 2,
                                tension: 0.4,
                                // 为柱状图添加圆角
                                borderRadius: chartType === 'bar' ? 6 : 0,
                                maxBarThickness: 40
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            layout: {
                                padding: {
                                    top: 10,
                                    right: 10,
                                    bottom: 10,
                                    left: 5
                                }
                            },
                            animations: {
                                tension: {
                                    duration: 1000,
                                    easing: 'easeOutQuart',
                                    from: 0.8,
                                    to: 0.4,
                                    loop: false
                                }
                            },
                            scales: {
                                x: {
                                    type: 'time',
                                    min: chartData.labels && chartData.labels.length > 0 ? chartData.labels[0] : undefined,
                                    max: chartData.labels && chartData.labels.length > 0 ? chartData.labels[chartData.labels.length - 1] : undefined,
                                    time: {
                                        // 根据日期范围自动选择合适的单位
                                        unitStepSize: 1,
                                        displayFormats: {
                                            millisecond: 'HH:mm:ss.SSS',
                                            second: 'HH:mm:ss',
                                            minute: 'HH:mm',
                                            hour: 'MM-DD HH:mm',
                                            day: 'MM-DD',
                                            week: 'YY-MM-DD',
                                            month: 'YY-MM',
                                            quarter: 'YY [Q]Q',
                                            year: 'YYYY'
                                        },
                                        // 适应设定的时间范围
                                        bounds: 'ticks',
                                        // 自动确定最佳显示单位
                                        distribution: 'linear'
                                    },
                                    title: {
                                        display: true,
                                        text: '时间',
                                        font: {
                                            size: 14,
                                            weight: 'bold'
                                        },
                                        padding: {top: 10, bottom: 0}
                                    },
                                    grid: {
                                        display: true,
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawBorder: false
                                    },
                                    ticks: {
                                        maxRotation: 0,
                                        autoSkip: true,
                                        font: {
                                            size: 11
                                        },
                                        color: '#666'
                                    }
                                },
                                y: {
                                    title: {
                                        display: true,
                                        text: deviceData.id === 'ph' ? 'PH值' : '水位 (%)',
                                        font: {
                                            size: 14,
                                            weight: 'bold'
                                        },
                                        padding: {top: 0, bottom: 10}
                                    },
                                    beginAtZero: true,
                                    // 为不同设备设置不同的刻度范围
                                    min: 0,
                                    max: deviceData.id === 'ph' ? 14 : 100,
                                    grid: {
                                        display: true,
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawBorder: false
                                    },
                                    ticks: {
                                        font: {
                                            size: 11
                                        },
                                        color: '#666',
                                        padding: 8,
                                        callback: function(value) {
                                            return deviceData.id === 'ph' ? value : value + '%';
                                        }
                                    }
                                }
                            },
                            plugins: {
                                tooltip: {
                                    mode: chartType === 'line' ? 'index' : 'nearest',
                                    intersect: false,
                                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                    titleFont: {
                                        size: 13,
                                        weight: 'bold'
                                    },
                                    bodyFont: {
                                        size: 12
                                    },
                                    padding: 10,
                                    cornerRadius: 6,
                                    displayColors: false,
                                    callbacks: {
                                        label: function(context) {
                                            let label = context.dataset.label || '';
                                            if (label) {
                                                label += ': ';
                                            }
                                            if (context.parsed.y !== null) {
                                                label += deviceData.id === 'ph' ? 
                                                    context.parsed.y.toFixed(2) : 
                                                    context.parsed.y.toFixed(1) + '%';
                                            }
                                            return label;
                                        }
                                    }
                                },
                                legend: {
                                    display: true,
                                    position: 'top',
                                    align: 'end',
                                    labels: {
                                        boxWidth: 12,
                                        usePointStyle: true,
                                        pointStyle: 'circle',
                                        padding: 20,
                                        font: {
                                            size: 12
                                        }
                                    }
                                }
                            },
                            // 添加交互效果
                            hover: {
                                mode: 'index',
                                intersect: false
                            }
                        }
                    };
                    
                    // 如果没有数据，添加中央异常提示
                    if (!hasData) {
                        chartConfig.options.plugins.annotation = {
                            annotations: {
                                noDataLabel: {
                                    type: 'label',
                                    xValue: chartData.labels[Math.floor(chartData.labels.length / 2)],
                                    yValue: deviceData.id === 'ph' ? 7 : 50,
                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                    content: ['数据异常', '显示为0'],
                                    font: {
                                        size: 16,
                                        weight: 'bold'
                                    },
                                    color: 'rgba(244, 67, 54, 0.8)',
                                    padding: 8,
                                    borderRadius: 4
                                }
                            }
                        };
                    }
                    
                    // 创建图表
                    window.dataChart = new Chart(ctx, chartConfig);
                }
                
                // 辅助函数：将十六进制颜色转换为RGB格式
                function hexToRgb(hex) {
                    // 去除可能的#前缀
                    hex = hex.replace(/^#/, '');
                    
                    // 解析十六进制颜色
                    const bigint = parseInt(hex, 16);
                    const r = (bigint >> 16) & 255;
                    const g = (bigint >> 8) & 255;
                    const b = bigint & 255;
                    
                    return r + ',' + g + ',' + b;
                }
                
                                    // 更新数据统计
                function updateDataStatistics(values, hasData) {
                    // 如果没有数据，所有统计值都显示为0
                    if (!hasData || values.length === 0) {
                        document.getElementById('avgValue').textContent = '0';
                        document.getElementById('maxValue').textContent = '0';
                        document.getElementById('minValue').textContent = '0';
                        document.getElementById('stdValue').textContent = '0';
                        return;
                    }
                    
                    // 计算统计值
                    const sum = values.reduce((a, b) => a + b, 0);
                    const avg = sum / values.length;
                    const max = Math.max(...values);
                    const min = Math.min(...values);
                    
                    // 计算标准差
                    const squareDiffs = values.map(value => {
                        const diff = value - avg;
                        return diff * diff;
                    });
                    const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;
                    const std = Math.sqrt(avgSquareDiff);
                    
                    // 更新显示（根据设备类型显示不同的统计标签）
                    const avgElement = document.getElementById('avgValue');
                    const maxElement = document.getElementById('maxValue');
                    const minElement = document.getElementById('minValue');
                    const stdElement = document.getElementById('stdValue');
                    
                    // 设置正确的统计数据标签
                    const summaryLabels = document.querySelectorAll('.summary-label');
                    if (summaryLabels && summaryLabels.length >= 1 && deviceData.id === 'ph') {
                        summaryLabels[0].textContent = '平均PH值:';
                    } else if (summaryLabels && summaryLabels.length >= 1 && deviceData.id === 'water') {
                        summaryLabels[0].textContent = '平均水位:';
                    }
                    
                    if (avgElement) avgElement.textContent = avg.toFixed(2) + (deviceData.id === 'water' ? '%' : '');
                    if (maxElement) maxElement.textContent = max.toFixed(2) + (deviceData.id === 'water' ? '%' : '');
                    if (minElement) minElement.textContent = min.toFixed(2) + (deviceData.id === 'water' ? '%' : '');
                    if (stdElement) stdElement.textContent = std.toFixed(2) + (deviceData.id === 'water' ? '%' : '');
                    
                    // 添加动画效果以引起注意
                    [avgElement, maxElement, minElement, stdElement].forEach(el => {
                        if (el) {
                            el.style.animation = 'none';
                            setTimeout(() => {
                                el.style.animation = 'pulse 0.5s ease-in-out';
                            }, 10);
                        }
                    });
                    
                    // 更新设备状态信息（仅当是传感器时）
                    if (deviceData.id === 'ph' || deviceData.id === 'water') {
                        // 检查函数是否存在，再调用
                        if (typeof updateDeviceCurrentStatus === 'function') {
                        updateDeviceCurrentStatus(avg.toFixed(2));
                        } else {
                            // 直接更新状态元素
                            const deviceStatusElement = document.getElementById('deviceStatus');
                            if (deviceStatusElement) {
                                deviceStatusElement.textContent = avg.toFixed(2) + (deviceData.id === 'water' ? '%' : '');
                                deviceStatusElement.style.animation = 'none';
                                setTimeout(() => {
                                    deviceStatusElement.style.animation = 'pulse 0.5s ease-in-out';
                                }, 10);
                            }
                        }
                    }
                    
                    // 更新波形图统计信息
                    updateWaveformStatistics(values);
                }
                
                // 显示图表错误
                function showChartError(container, loadingElement, message) {
                    // 移除加载指示器
                    if (loadingElement && loadingElement.parentNode) {
                        loadingElement.parentNode.removeChild(loadingElement);
                    }
                    
                    // 创建一个当前时间和24小时前的时间范围
                    const currentTime = new Date();
                    const startTime = new Date(currentTime.getTime() - 24 * 60 * 60 * 1000);
                    
                    // 创建默认数据点显示为0并标记异常
                    const timePoints = generateTimePoints(startTime, currentTime, period);
                    const chartData = {
                        labels: timePoints,
                        values: timePoints.map(() => 0)
                    };
                    
                    // 创建图表，标记为无数据
                    createChart(ctx, chartData, chartType, false);
                    
                    // 更新数据统计为0
                    updateDataStatistics([], false);
                    
                    // 显示错误提示
                    showToast(message, 'warning');
                }
            }
        }
        
        // 使用日期范围初始化预测图表
        function initPredictionChartWithDateRange(startDate, endDate) {
            const canvas = document.getElementById('predictionChart');
            if (!canvas) {
                console.error('找不到预测图表Canvas元素');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('无法获取预测图表上下文');
                return;
            }
            
            // 为图表容器添加加载指示器
            const chartContainer = canvas.parentElement;
            if (chartContainer) {
                const loadingSpinner = document.createElement('div');
                loadingSpinner.className = 'prediction-loading-spinner';
                loadingSpinner.style.position = 'absolute';
                loadingSpinner.style.top = '50%';
                loadingSpinner.style.left = '50%';
                loadingSpinner.style.transform = 'translate(-50%, -50%)';
                loadingSpinner.style.width = '40px';
                loadingSpinner.style.height = '40px';
                loadingSpinner.style.border = '3px solid rgba(0, 0, 0, 0.1)';
                loadingSpinner.style.borderTopColor = '#9c27b0';
                loadingSpinner.style.borderRadius = '50%';
                loadingSpinner.style.animation = 'spin 1s linear infinite';
                loadingSpinner.style.zIndex = '10';
                
                chartContainer.style.position = 'relative';
                chartContainer.appendChild(loadingSpinner);
                
                // 检查是否为PH或水位传感器
                if (deviceData.id !== 'ph' && deviceData.id !== 'water') {
                    console.error('不支持的设备类型:', deviceData.id);
                    showPredictionError(chartContainer, loadingSpinner, '此设备类型不支持预测分析');
                    return;
                }
                
                // 获取实际历史数据并进行预测分析
                try {
                    // 创建token
                    const params = {
                        author_key: '****************************************************************',
                        version: '2022-05-01',
                        user_id: '420568',
                    };

                    const token = createCommonTokenFunc(params);
                    
                    // 确定时间范围 - 获取30天的历史数据用于预测分析
                    const now = new Date();
                    const start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    
                    // 根据设备类型确定数据流ID
                    const datastream_id = getCorrectDatastreamId(deviceData.id);
                    
                    // 构建请求URL
                    const url = new URL('https://iot-api.heclouds.com/thingmodel/query-device-property-history');
                    url.searchParams.append('product_id', 'HTJ98Pjh4a');
                    url.searchParams.append('device_name', 'flotation');
                    url.searchParams.append('identifier', datastream_id);
                    url.searchParams.append('start_time', start.getTime());
                    url.searchParams.append('end_time', now.getTime());
                    url.searchParams.append('limit', '1000');
                    
                    // 获取历史数据
                    fetch(url, {
                        method: 'GET',
                        headers: {
                            'authorization': token
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(responseData => {
                        // 移除加载指示器
                        if (loadingSpinner.parentNode) {
                            loadingSpinner.parentNode.removeChild(loadingSpinner);
                        }
                        
                        let historicalData = [];
                        
                        // 处理API返回的数据
                        if (responseData && responseData.code === 0 && 
                            responseData.data && responseData.data.list && 
                            responseData.data.list.length > 0) {
                            
                            const datapoints = responseData.data.list;
                            
                            // 确保数据点按时间排序
                            datapoints.sort((a, b) => parseInt(a.time) - parseInt(b.time));
                            
                            // 转换数据格式
                            historicalData = datapoints.map(point => ({
                                x: new Date(parseInt(point.time)),
                                y: parseFloat(point.value)
                            }));
                        }
                        
                        // 检查是否有足够的数据点进行预测
                        if (historicalData.length < 2) {
                            // 如果实际数据不足，使用模拟数据进行预测
                            console.log('实际历史数据不足，使用模拟数据进行预测');
                            
                            // 生成模拟数据
                            historicalData = generateHistoricalData(now);
                            console.log(`生成了${historicalData.length}个模拟历史数据点用于预测`);
                            
                            // 添加警告提示
                            showToast('API数据不足，使用模拟数据进行预测展示', 'warning');
                        } else {
                            console.log(`成功获取到${historicalData.length}个历史数据点用于预测`);
                        }
                        
                        // 生成预测数据 (未来7天)及误差带
                        const { predictionData, upperBoundData, lowerBoundData } = generatePredictionWithErrorBands(historicalData);
                        
                        // 创建图表，包括误差带
                        createPredictionChart(historicalData, predictionData, upperBoundData, lowerBoundData);
                        
                        // 更新预测洞察文本
                        updatePredictionInsight(historicalData, predictionData);
                        
                        // 设置自动刷新 - 当新的数据到达时更新预测
                        window.predictionAutoRefresh = setInterval(() => {
                            refreshPredictionData();
                        }, 60000); // 每60秒检查一次新数据
                    })
                    .catch(error => {
                        console.error('获取预测数据失败:', error);
                        
                        // 移除加载指示器
                        if (loadingSpinner && loadingSpinner.parentNode) {
                            loadingSpinner.parentNode.removeChild(loadingSpinner);
                        }
                        
                        // 使用模拟数据作为备用
                        console.log('API调用失败，使用模拟数据代替');
                        showToast('API调用失败，使用模拟数据进行预测展示', 'warning');
                        
                        // 生成模拟历史数据
                        const historicalData = generateHistoricalData(new Date());
                        console.log(`生成了${historicalData.length}个模拟历史数据点用于预测`);
                        
                        // 生成预测数据
                        const { predictionData, upperBoundData, lowerBoundData } = generatePredictionWithErrorBands(historicalData);
                        
                        // 创建图表
                        createPredictionChart(historicalData, predictionData, upperBoundData, lowerBoundData);
                        
                        // 更新预测洞察文本
                        updatePredictionInsight(historicalData, predictionData);
                        
                        // 设置自动刷新
                        window.predictionAutoRefresh = setInterval(() => {
                            refreshPredictionData();
                        }, 60000); // 每60秒检查一次新数据
                    });
                } catch (error) {
                    console.error('预测分析初始化出错:', error);
                    
                    // 移除加载指示器
                    if (loadingSpinner && loadingSpinner.parentNode) {
                        loadingSpinner.parentNode.removeChild(loadingSpinner);
                    }
                    
                    // 使用模拟数据作为备用
                    console.log('预测分析初始化失败，使用模拟数据代替');
                    showToast('初始化失败，使用模拟数据进行预测展示', 'warning');
                    
                    // 生成模拟历史数据
                    const historicalData = generateHistoricalData(new Date());
                    console.log(`生成了${historicalData.length}个模拟历史数据点用于预测`);
                    
                    // 生成预测数据
                    const { predictionData, upperBoundData, lowerBoundData } = generatePredictionWithErrorBands(historicalData);
                    
                    // 创建图表
                    createPredictionChart(historicalData, predictionData, upperBoundData, lowerBoundData);
                    
                    // 更新预测洞察文本
                    updatePredictionInsight(historicalData, predictionData);
                    
                    // 设置自动刷新
                    window.predictionAutoRefresh = setInterval(() => {
                        refreshPredictionData();
                    }, 60000); // 每60秒检查一次新数据
                }
                
                // 生成模拟历史数据
                function generateHistoricalData(endDate) {
                    const data = [];
                    const daysToGenerate = 30; // 生成30天的历史数据
                    const dataPointsPerDay = 4; // 每天4个数据点
                    const totalPoints = daysToGenerate * dataPointsPerDay;
                    const isPH = deviceData.id === 'ph';
                    
                    // 设备特定的基础参数
                    const baseValue = isPH ? 7.0 : 80.0;  // PH值基准为7.0，水位基准为80%
                    const trendFactor = isPH ? 0.003 : 0.1;  // 长期趋势因子
                    const dailyCycleFactor = isPH ? 0.2 : 5;  // 日循环波动因子
                    const noiseFactor = isPH ? 0.1 : 2;  // 噪声因子 - PH值波动更小
                    const seasonalFactor = isPH ? 0.2 : 7;  // 季节性因子
                    
                    // 周期性模式参数
                    const dailyCycleFreq = 2 * Math.PI / dataPointsPerDay;  // 日循环频率
                    const weekCycleFreq = 2 * Math.PI / (7 * dataPointsPerDay);  // 周循环频率
                    const phaseShift = Math.random() * Math.PI * 2;  // 随机相位
                    
                    for (let i = 0; i < totalPoints; i++) {
                        const millisToSubtract = (totalPoints - i) * (24 / dataPointsPerDay) * 60 * 60 * 1000;
                        const pointDate = new Date(endDate.getTime() - millisToSubtract);
                        
                        // 计算时间相关的循环因子
                        const dayOfYear = pointDate.getDate() + pointDate.getMonth() * 30;
                        const timeOfDay = pointDate.getHours() + pointDate.getMinutes() / 60;
                        
                        // 日循环模式 (时间依赖) - PH值在白天略高，晚上略低；水位在使用高峰期略低
                        const dailyCycle = Math.sin(dailyCycleFreq * timeOfDay + phaseShift) * dailyCycleFactor;
                        
                        // 周循环模式 - 工作日和周末模式不同
                        const dayOfWeek = pointDate.getDay(); // 0-6
                        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
                        const weekendFactor = isWeekend ? 1.2 : 0.8;
                        const weekCycle = Math.sin(weekCycleFreq * i + phaseShift) * (dailyCycleFactor * 0.5) * weekendFactor;
                        
                        // 季节性模式 (假设一年中的位置)
                        const seasonalCycle = Math.sin(2 * Math.PI * dayOfYear / 365) * seasonalFactor;
                        
                        // 长期趋势 (略微上升或下降)
                        const trend = (Math.random() > 0.5 ? 1 : -1) * trendFactor * i / dataPointsPerDay;
                        
                        // 随机噪声
                        const noise = (Math.random() * 2 - 1) * noiseFactor;
                        
                        // 计算最终值
                        let value = baseValue + dailyCycle + weekCycle + seasonalCycle + trend + noise;
                        
                        // 确保值在合理范围内
                        if (isPH) {
                            value = Math.max(6.0, Math.min(8.0, value)); // 更实际的PH值范围: 6.0-8.0
                        } else {
                            value = Math.max(65, Math.min(95, value)); // 更实际的水位范围: 65%-95%
                        }
                        
                        data.push({
                            x: pointDate,
                            y: value
                        });
                    }
                    
                    return data;
                }
                
                // 刷新预测数据的函数
                function refreshPredictionData() {
                    console.log('正在检查新的数据以更新预测...');
                    
                    // 避免在无法访问API时反复失败
                    if (!createCommonTokenFunc) {
                        console.warn('无法访问API，取消自动刷新');
                        if (window.predictionAutoRefresh) {
                            clearInterval(window.predictionAutoRefresh);
                        }
                        return;
                    }
                    
                    try {
                        const params = {
                            author_key: '****************************************************************',
                            version: '2022-05-01',
                            user_id: '420568',
                        };
                        const token = createCommonTokenFunc(params);
                        
                        // 获取最新的一小时数据看是否有更新
                        const now = new Date();
                        const oneHourAgo = new Date(now.getTime() - 1 * 60 * 60 * 1000);
                        
                        // 根据设备类型确定数据流ID
                        const datastream_id = getCorrectDatastreamId(deviceData.id);
                        
                        // 构建请求URL
                        const url = new URL('https://iot-api.heclouds.com/thingmodel/query-device-property-history');
                        url.searchParams.append('product_id', 'HTJ98Pjh4a');
                        url.searchParams.append('device_name', 'flotation');
                        url.searchParams.append('identifier', datastream_id);
                        url.searchParams.append('start_time', oneHourAgo.getTime());
                        url.searchParams.append('end_time', now.getTime());
                        url.searchParams.append('limit', '10');
                        
                        // 获取最新数据
                        fetch(url, {
                            method: 'GET',
                            headers: {
                                'authorization': token
                            }
                        })
                        .then(response => response.json())
                        .then(responseData => {
                            if (responseData && responseData.code === 0 && 
                                responseData.data && responseData.data.list && 
                                responseData.data.list.length > 0) {
                                
                                console.log(`检测到${responseData.data.list.length}个新数据点，重新生成预测`);
                                
                                // 如果有新数据，重新加载预测图表
                                if (window.predictionChart) {
                                    window.predictionChart.destroy();
                                    window.predictionChart = null;
                                }
                                
                                // 重新初始化预测图表
                                const endPrediction = new Date();
                                const startPrediction = new Date(endPrediction.getTime() - (7 * 24 * 60 * 60 * 1000));
                                initPredictionChartWithDateRange(startPrediction, endPrediction);
                            } else {
                                console.log('没有检测到新数据');
                            }
                        })
                        .catch(error => {
                            console.error('刷新预测数据失败:', error);
                            // 失败次数过多时停止自动刷新
                            if (window.predictionRefreshErrors === undefined) {
                                window.predictionRefreshErrors = 0;
                            }
                            window.predictionRefreshErrors++;
                            
                            if (window.predictionRefreshErrors > 5) {
                                console.warn('自动刷新失败次数过多，停止自动刷新');
                                if (window.predictionAutoRefresh) {
                                    clearInterval(window.predictionAutoRefresh);
                                }
                            }
                        });
                    } catch (error) {
                        console.error('刷新预测数据时出错:', error);
                    }
                }
                
                // 显示预测图表错误信息
                function showPredictionError(container, spinner, message) {
                    // 移除加载指示器
                    if (spinner && spinner.parentNode) {
                        spinner.parentNode.removeChild(spinner);
                    }
                    
                    // 创建错误信息显示
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'prediction-error';
                    errorDiv.style.position = 'absolute';
                    errorDiv.style.top = '0';
                    errorDiv.style.left = '0';
                    errorDiv.style.width = '100%';
                    errorDiv.style.height = '100%';
                    errorDiv.style.display = 'flex';
                    errorDiv.style.flexDirection = 'column';
                    errorDiv.style.justifyContent = 'center';
                    errorDiv.style.alignItems = 'center';
                    errorDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
                    errorDiv.style.zIndex = '5';
                    errorDiv.style.padding = '20px';
                    errorDiv.style.textAlign = 'center';
                    
                    errorDiv.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#9c27b0" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-bottom: 10px;">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                        <div style="font-size: 16px; font-weight: 600; color: #9c27b0; margin-bottom: 8px;">预测分析不可用</div>
                        <div style="font-size: 14px; color: #666; margin-bottom: 15px;">${message}</div>
                        <button id="retryPredictionButton" style="background: #9c27b0; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            重试
                        </button>
                    `;
                    
                    container.appendChild(errorDiv);
                    
                    // 添加重试按钮事件
                    const retryButton = errorDiv.querySelector('#retryPredictionButton');
                    if (retryButton) {
                        retryButton.addEventListener('click', () => {
                            // 移除错误信息
                            if (errorDiv.parentNode) {
                                errorDiv.parentNode.removeChild(errorDiv);
                            }
                            
                            // 重新加载预测图表
                            setTimeout(() => {
                                // 使用日期范围版本
                                const endDate = new Date();
                                const startDate = new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000));
                                initPredictionChartWithDateRange(startDate, endDate);
                            }, 100);
                        });
                    }
                    
                    // 清空预测洞察文本
                    const insightElement = document.getElementById('predictionInsight');
                    if (insightElement) {
                        insightElement.textContent = '无法获取预测数据。';
                    }
                    
                    // 显示提示
                    showToast('预测分析加载失败: ' + message, 'warning');
                }
                
                // 生成预测数据 - 使用高级自适应时间序列分析模型并添加误差带
                function generatePredictionWithErrorBands(historicalData) {
                    console.log('使用高级自适应时间序列分析模型生成预测数据和误差带，基于', historicalData.length, '个历史数据点');
                    
                    // 确保数据足够进行预测 - 只要有2个点及以上就能预测
                    if (historicalData.length < 2) {
                        console.error('数据点不足，无法进行预测，至少需要2个数据点');
                        return { predictionData: [], upperBoundData: [], lowerBoundData: [] }; 
                    }
                    
                    // 根据数据点数量自动调整预测复杂度
                    const predictionComplexity = Math.min(1.0, historicalData.length / 50); // 0.0-1.0
                    console.log(`预测复杂度系数: ${predictionComplexity.toFixed(2)} (基于数据点数量)`);
                    
                    
                    // 使用所有可用数据，但更重视最近的数据
                    const allData = [...historicalData];
                    // 按时间顺序排序
                    allData.sort((a, b) => a.x.getTime() - b.x.getTime());
                    
                    const isPH = deviceData.id === 'ph';
                    
                    // 1. 高级数据特征提取
                    
                    // 计算基本统计特征
                    const values = allData.map(point => point.y);
                    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
                    const variance = values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / values.length;
                    const stdDev = Math.sqrt(variance);
                    
                    // 计算最大变化率和平均变化率
                    let maxChangeRate = 0;
                    let totalChangeRate = 0;
                    let changeRateCount = 0;
                    
                    for (let i = 1; i < allData.length; i++) {
                        const timeDiff = (allData[i].x.getTime() - allData[i-1].x.getTime()) / (1000 * 60 * 60); // 小时
                        if (timeDiff > 0) {
                            const changeRate = Math.abs((allData[i].y - allData[i-1].y) / timeDiff);
                            maxChangeRate = Math.max(maxChangeRate, changeRate);
                            totalChangeRate += changeRate;
                            changeRateCount++;
                        }
                    }
                    
                    const avgChangeRate = changeRateCount > 0 ? totalChangeRate / changeRateCount : 0;
                    
                    // 2. 复杂时间模式识别
                    
                    // 小时级周期模式 (24小时循环)
                    const hourlyPatterns = new Array(24).fill(0);
                    const hourlyCount = new Array(24).fill(0);
                    
                    // 日级周期模式 (7天循环)
                    const dailyPatterns = new Array(7).fill(0);
                    const dailyCount = new Array(7).fill(0);
                    
                    // 分析历史数据中的周期性模式
                    allData.forEach(point => {
                        const hour = point.x.getHours();
                        const day = point.x.getDay(); // 0-6
                        
                        hourlyPatterns[hour] += point.y;
                        hourlyCount[hour]++;
                        
                        dailyPatterns[day] += point.y;
                        dailyCount[day]++;
                    });
                    
                    // 计算每个小时的平均偏差
                    for (let i = 0; i < 24; i++) {
                        hourlyPatterns[i] = hourlyCount[i] > 0 ? 
                            (hourlyPatterns[i] / hourlyCount[i]) - avg : 0;
                    }
                    
                    // 计算每天的平均偏差
                    for (let i = 0; i < 7; i++) {
                        dailyPatterns[i] = dailyCount[i] > 0 ? 
                            (dailyPatterns[i] / dailyCount[i]) - avg : 0;
                    }
                    
                    // 3. 趋势分析 - 使用线性回归
                    
                    // 将时间转换为数值以进行线性回归
                    const startTime = allData[0].x.getTime();
                    const timeScale = 24 * 60 * 60 * 1000; // 1天的毫秒数
                    
                    let sumX = 0;
                    let sumY = 0;
                    let sumXY = 0;
                    let sumX2 = 0;
                    
                    // 使用加权线性回归，最近的数据权重更高
                    allData.forEach((point, index) => {
                        // 归一化时间值（天数）
                        const x = (point.x.getTime() - startTime) / timeScale;
                        const y = point.y;
                        
                        // 最近数据权重更高
                        const weight = 0.5 + 0.5 * (index / allData.length);
                        
                        sumX += x * weight;
                        sumY += y * weight;
                        sumXY += (x * y) * weight;
                        sumX2 += (x * x) * weight;
                    });
                    
                    const n = allData.length;
                    
                    // 计算线性回归系数
                    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
                    const intercept = (sumY - slope * sumX) / n;
                    
                    console.log(`线性回归: y = ${slope.toFixed(4)} * x + ${intercept.toFixed(4)}`);
                    
                    // 4. 复杂变动因子分析 - 识别季节性、突变和异常点
                    
                    // 计算趋势线上的值，并识别异常点
                    const deviations = allData.map(point => {
                        const x = (point.x.getTime() - startTime) / timeScale;
                        const trendValue = slope * x + intercept;
                        return point.y - trendValue; // 与趋势线的偏差
                    });
                    
                    // 计算偏差的标准差，用于识别异常点
                    const deviationStd = Math.sqrt(
                        deviations.reduce((sum, d) => sum + d * d, 0) / deviations.length
                    );
                    
                    // 计算异常点阈值
                    const anomalyThreshold = 2.5 * deviationStd;
                    
                    // 检测异常点
                    const anomalies = deviations.filter(d => Math.abs(d) > anomalyThreshold).length;
                    const anomalyRatio = anomalies / deviations.length;
                    
                    console.log(`检测到${anomalies}个异常点，异常率: ${(anomalyRatio * 100).toFixed(2)}%`);
                    
                    // 5. 多因素预测模型 - 结合趋势、周期性和随机性
                    
                    // 获取最后一个数据点
                    const lastPoint = allData[allData.length - 1];
                    const lastTime = lastPoint.x.getTime();
                    
                    // 生成预测数据 - 使用标准7天预测
                    const predictionData = [];
                    
                    // 设置预测参数 - 恢复为标准7天预测
                    const predictionDays = 7;
                    const pointsPerDay = 4; // 每天4个点，每6小时一个
                    
                    // 计算趋势稳定性 - 将影响随机因子的大小
                    const trendStability = 1.0 - anomalyRatio;
                    
                    // 预测天气
                    for (let day = 0; day < predictionDays; day++) {
                        for (let i = 0; i < pointsPerDay; i++) {
                            // 计算预测的时间点
                            const pointTime = new Date(lastTime + (day * 24 + i * (24 / pointsPerDay)) * 60 * 60 * 1000);
                            const dayOfWeek = pointTime.getDay();
                            const hour = pointTime.getHours();
                            
                            // 预测时间（天数）
                            const x = (pointTime.getTime() - startTime) / timeScale;
                            
                            // 基础预测值（趋势线）
                            let predictedValue = slope * x + intercept;
                            
                            // 添加小时级周期性模式
                            predictedValue += hourlyPatterns[hour] || 0;
                            
                            // 添加日级周期性模式
                            predictedValue += dailyPatterns[dayOfWeek] || 0;
                            
                            // 不确定性随时间增加
                            const uncertaintyFactor = 0.1 + (day / predictionDays) * 0.4; // 从0.1增加到0.5
                            
                            // 添加随机扰动（稳定性越高，扰动越小）
                            const randomNoise = (Math.random() - 0.5) * 2 * stdDev * uncertaintyFactor * (1 - trendStability);
                            predictedValue += randomNoise;
                            
                            // 确保预测值在合理范围内
                            if (isPH) {
                                // PH值范围: 0-14，正常范围: 6.0-8.0
                                predictedValue = Math.max(4.0, Math.min(10.0, predictedValue));
                            } else {
                                // 水位百分比范围: 0-100，正常范围: 65-95
                                predictedValue = Math.max(50, Math.min(100, predictedValue));
                            }
                            
                            predictionData.push({
                                x: pointTime,
                                y: predictedValue
                            });
                        }
                    }
                    
                    // 6. 平滑预测曲线并生成误差带
                    
                    // 使用简单移动平均进行平滑处理
                    const smoothedData = [];
                    const upperBoundData = [];
                    const lowerBoundData = [];
                    const smoothingWindow = 3; // 平滑窗口大小
                    const errorMargin = 0.05; // 5%的误差带
                    
                    for (let i = 0; i < predictionData.length; i++) {
                        let sum = 0;
                        let count = 0;
                        
                        // 计算窗口内的平均值
                        for (let j = Math.max(0, i - smoothingWindow + 1); j <= i; j++) {
                            if (j < predictionData.length) {
                                sum += predictionData[j].y;
                                count++;
                            }
                        }
                        
                        // 创建平滑后的数据点
                        const smoothedValue = sum / count;
                        const pointTime = predictionData[i].x;
                        
                        smoothedData.push({
                            x: pointTime,
                            y: smoothedValue
                        });
                        
                        // 创建误差带数据点 - 上下各5%
                        upperBoundData.push({
                            x: pointTime,
                            y: smoothedValue * (1 + errorMargin)
                        });
                        
                        lowerBoundData.push({
                            x: pointTime,
                            y: smoothedValue * (1 - errorMargin)
                        });
                    }
                    
                    console.log(`成功生成${smoothedData.length}个预测数据点，带有5%误差带`);
                    
                    // 返回预测数据和误差带
                    return {
                        predictionData: smoothedData,
                        upperBoundData: upperBoundData,
                        lowerBoundData: lowerBoundData
                    };
                }
                
                // 创建预测图表
                function createPredictionChart(historicalData, predictionData, upperBoundData, lowerBoundData) {
                    // 销毁现有图表
                    if (window.predictionChart && typeof window.predictionChart.destroy === 'function') {
                        window.predictionChart.destroy();
                        window.predictionChart = null;
                    }
                    
                    // 创建闭合的误差区域数据 - 使用传入的误差带
                    const errorAreaData = [
                        ...upperBoundData, 
                        ...(lowerBoundData.slice().reverse()) // 反转下边界以创建闭合区域
                    ];
                    
                    // 设置图表配置
                    const chartConfig = {
                        type: 'line',
                        data: {
                            datasets: [
                                {
                                    label: '历史数据',
                                    data: historicalData.slice(-14), // 显示最近14个数据点
                                    borderColor: '#2196f3',
                                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                                    borderWidth: 2,
                                    pointRadius: 3,
                                    tension: 0.3,
                                    fill: false
                                },
                                {
                                    label: '预测数据',
                                    data: predictionData,
                                    borderColor: '#9c27b0',
                                    backgroundColor: 'rgba(156, 39, 176, 0.1)',
                                    borderWidth: 2,
                                    borderDash: [5, 5],
                                    pointRadius: 2,
                                    tension: 0.3,
                                    fill: false
                                },
                                {
                                    label: '误差范围 (±5%)',
                                    data: errorAreaData,
                                    backgroundColor: 'rgba(156, 39, 176, 0.1)',
                                    borderColor: 'rgba(156, 39, 176, 0.2)',
                                    borderWidth: 1,
                                    pointRadius: 0,
                                    fill: true
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animation: {
                                duration: 1000,
                                easing: 'easeOutQuart'
                            },
                            scales: {
                                x: {
                                    type: 'time',
                                    time: {
                                        unit: 'day',
                                        displayFormats: {
                                            day: 'MM-DD'
                                        },
                                        tooltipFormat: 'yyyy-MM-dd HH:mm'
                                    },
                                    title: {
                                        display: true,
                                        text: '日期'
                                    }
                                },
                                y: {
                                    title: {
                                        display: true,
                                        text: deviceData.id === 'ph' ? 'PH值' : '水位 (%)'
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: true,
                                    position: 'top',
                                    labels: {
                                        boxWidth: 12,
                                        font: {
                                            size: 10
                                        }
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                    titleFont: {
                                        size: 12
                                    },
                                    bodyFont: {
                                        size: 11
                                    },
                                    padding: 8,
                                    displayColors: false
                                }
                            },
                            interaction: {
                                intersect: false,
                                mode: 'index'
                            }
                        }
                    };
                    
                    // 创建图表
                    window.predictionChart = new Chart(ctx, chartConfig);
                }
                
                // 更新预测洞察文本
                function updatePredictionInsight(historicalData, predictionData) {
                    if (!historicalData || historicalData.length === 0 || !predictionData || predictionData.length === 0) {
                        document.getElementById('predictionInsight').textContent = '无法生成预测分析，数据不足。';
                        return;
                    }
                    
                    // 计算趋势
                    const firstPrediction = predictionData[0].y;
                    const lastPrediction = predictionData[predictionData.length - 1].y;
                    const trend = lastPrediction - firstPrediction;
                    
                    // 计算历史数据的平均值和标准差
                    const values = historicalData.map(point => point.y);
                    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
                    const variance = values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / values.length;
                    const stdDev = Math.sqrt(variance);
                    
                    // 根据设备类型和趋势生成洞察文本
                    let insightText = '';
                    
                    if (deviceData.id === 'ph') {
                        if (Math.abs(trend) < stdDev * 0.2) {
                            insightText = `根据历史数据分析，预计PH值在未来7天内将保持稳定，维持在 ${avg.toFixed(1)} 左右。建议继续保持当前的监测频率。`;
                        } else if (trend > 0) {
                            insightText = `预测显示PH值在未来7天内有上升趋势，可能达到 ${lastPrediction.toFixed(1)}。建议密切关注，如果超过7.5可能需要调整。`;
                        } else {
                            insightText = `预测显示PH值在未来7天内有下降趋势，可能降至 ${lastPrediction.toFixed(1)}。建议密切关注，如果低于6.5可能需要调整。`;
                        }
                    } else if (deviceData.id === 'water') {
                        if (Math.abs(trend) < stdDev * 0.2) {
                            insightText = `根据历史数据分析，预计水位在未来7天内将保持稳定，维持在 ${avg.toFixed(0)}% 左右。建议继续保持当前的监测频率。`;
                        } else if (trend > 0) {
                            insightText = `预测显示水位在未来7天内有上升趋势，可能达到 ${lastPrediction.toFixed(0)}%。建议密切关注，如果超过90%可能需要排水。`;
                        } else {
                            insightText = `预测显示水位在未来7天内有下降趋势，可能降至 ${lastPrediction.toFixed(0)}%。建议密切关注，如果低于70%可能需要补水。`;
                        }
                    }
                    
                    document.getElementById('predictionInsight').textContent = insightText;
                }
            }
        }
        
        // 设置时间周期选择器交互
        function setupPeriodSelector() {
            const periodOptions = document.querySelectorAll('.period-option');
            const timeInfo = document.querySelector('.time-info');
            
            // 确保默认有一个选中状态
            const defaultOption = document.querySelector('.period-option[data-period="24h"]');
            if (defaultOption && !defaultOption.classList.contains('active')) {
                defaultOption.classList.add('active');
            }
            
            periodOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除所有选项的活动状态
                    periodOptions.forEach(opt => {
                        opt.classList.remove('active');
                        opt.style.transform = '';
                        opt.style.backgroundColor = '';
                        opt.style.color = '';
                        opt.style.boxShadow = '';
                    });
                    
                    // 设置当前选项为活动状态
                    this.classList.add('active');
                    this.style.backgroundColor = '#222';
                    this.style.color = '#fff';
                    this.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.15)';
                    this.style.transform = 'translateY(-2px)';
                    
                    // 添加点击波纹效果
                    const ripple = document.createElement('span');
                    ripple.className = 'ripple-effect';
                    ripple.style.position = 'absolute';
                    ripple.style.top = '50%';
                    ripple.style.left = '50%';
                    ripple.style.width = '5px';
                    ripple.style.height = '5px';
                    ripple.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
                    ripple.style.borderRadius = '50%';
                    ripple.style.transform = 'translate(-50%, -50%)';
                    ripple.style.animation = 'ripple 0.6s ease-out';
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    // 移除波纹效果
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                    
                    // 获取选择的时间周期
                    const period = this.getAttribute('data-period');
                    
                    // 更新时间信息显示
                    if (timeInfo) {
                        switch(period) {
                            case '3d': timeInfo.textContent = '最近3天'; break;
                            case '5d': timeInfo.textContent = '最近5天'; break;
                            case '7d': timeInfo.textContent = '最近7天'; break;
                            case '15d': timeInfo.textContent = '最近15天'; break;
                            case '30d': timeInfo.textContent = '最近30天'; break;
                            default: timeInfo.textContent = '最近24小时';
                        }
                    }
                    
                    // 检查是否为PH或水位传感器
                    const isSensorDevice = deviceData.id === 'ph' || deviceData.id === 'water';
                    
                    // 为当前设备添加加载指示器
                    const loadingIndicator = document.createElement('div');
                    loadingIndicator.className = 'loading-indicator show';
                    loadingIndicator.style.position = 'absolute';
                    loadingIndicator.style.top = '0';
                    loadingIndicator.style.left = '0';
                    loadingIndicator.style.right = '0';
                    loadingIndicator.style.bottom = '0';
                    loadingIndicator.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
                    loadingIndicator.style.display = 'flex';
                    loadingIndicator.style.justifyContent = 'center';
                    loadingIndicator.style.alignItems = 'center';
                    loadingIndicator.style.zIndex = '100';
                    
                    const spinner = document.createElement('div');
                    spinner.className = 'loading-spinner';
                    spinner.style.width = '40px';
                    spinner.style.height = '40px';
                    spinner.style.border = '3px solid rgba(0, 0, 0, 0.1)';
                    spinner.style.borderTopColor = '#2196f3';
                    spinner.style.borderRadius = '50%';
                    spinner.style.animation = 'spin 1s linear infinite';
                    
                    loadingIndicator.appendChild(spinner);
                    document.querySelector('.history-content').appendChild(loadingIndicator);
                    
                    // 销毁现有图表以避免冲突
                    if (window.dataChart && typeof window.dataChart.destroy === 'function') {
                        window.dataChart.destroy();
                        window.dataChart = null;
                    }
                    if (window.predictionChart && typeof window.predictionChart.destroy === 'function') {
                        window.predictionChart.destroy();
                        window.predictionChart = null;
                    }
                    
                    // 延迟执行以确保加载指示器显示
                    setTimeout(() => {
                        try {
                            console.log("开始强制更新矩阵，选择时间：", period);
                            
                            // 强制清空并重新创建状态矩阵
                            const matrix = document.getElementById('statusMatrix');
                            if (matrix) {
                                matrix.innerHTML = '';
                                console.log("矩阵已清空，准备重新生成");
                            }
                            
                            // 强制更新状态矩阵
                            initStatusMatrix(period);
                            
                            // 只有PH传感器和水位传感器才需要更新图表
                            if (isSensorDevice) {
                                const chartType = document.querySelector('.toggle-option.active')?.getAttribute('data-view') || 'line';
                                
                                // 延迟加载图表
                                setTimeout(() => {
                                    try {
                                        // 使用日期范围版本
                                        const endDate = new Date();
                                        const startDate = new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000));
                                        initDataChartWithDateRange(startDate, endDate, chartType);
                                        initPredictionChartWithDateRange(startDate, endDate);
                                        
                                        // 移除加载指示器
                                        if (document.querySelector('.loading-indicator.show')) {
                                            document.querySelector('.loading-indicator.show').remove();
                                        }
                                    } catch (error) {
                                        console.error('更新图表时出错:', error);
                                        // 移除加载指示器
                                        if (document.querySelector('.loading-indicator.show')) {
                                            document.querySelector('.loading-indicator.show').remove();
                                        }
                                    }
                                }, 500);
                            } else {
                                // 如果不是传感器设备，立即移除加载指示器
                                if (document.querySelector('.loading-indicator.show')) {
                                    document.querySelector('.loading-indicator.show').remove();
                                }
                            }
                        } catch (error) {
                            console.error('更新时间周期出错:', error);
                            // 移除加载指示器
                            if (document.querySelector('.loading-indicator.show')) {
                                document.querySelector('.loading-indicator.show').remove();
                            }
                        }
                    }, 100);
                });
            });
            
            // 添加提示信息
            const timePeriodSelector = document.querySelector('.time-period-selector');
            if (timePeriodSelector) {
                const tooltip = document.createElement('div');
                tooltip.style.position = 'absolute';
                tooltip.style.bottom = '-12px';
                tooltip.style.left = '50%';
                tooltip.style.transform = 'translateX(-50%)';
                tooltip.style.fontSize = '10px';
                tooltip.style.color = '#666';
                tooltip.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
                tooltip.style.padding = '2px 8px';
                tooltip.style.borderRadius = '10px';
                tooltip.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                tooltip.style.opacity = '0';
                tooltip.style.transition = 'opacity 0.3s ease';
                tooltip.textContent = '点击选择时间范围';
                
                timePeriodSelector.style.position = 'relative';
                timePeriodSelector.appendChild(tooltip);
                
                setTimeout(() => {
                    tooltip.style.opacity = '1';
                }, 1000);
                
                setTimeout(() => {
                    tooltip.style.opacity = '0';
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.parentNode.removeChild(tooltip);
                        }
                    }, 300);
                }, 5000);
            }
        }
        
        // 设置图表类型切换
        function setupChartToggle() {
            const toggleOptions = document.querySelectorAll('.toggle-option');
            
            toggleOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 仅在PH传感器或水位传感器时才执行图表切换
                    if (deviceData.id !== 'ph' && deviceData.id !== 'water') {
                        return;
                    }
                    
                    // 如果已经是活动的，不执行任何操作
                    if (this.classList.contains('active')) {
                        return;
                    }
                    
                    // 添加点击视觉反馈
                    this.classList.add('pulse-animation');
                    setTimeout(() => {
                        this.classList.remove('pulse-animation');
                    }, 500);
                    
                    // 移除所有选项的活动状态
                    toggleOptions.forEach(opt => opt.classList.remove('active'));
                    
                    // 设置当前选项为活动状态
                    this.classList.add('active');
                    
                    // 获取选择的图表类型
                    const chartType = this.getAttribute('data-view');
                    
                    // 为图表容器添加加载指示器
                    const chartContainer = document.getElementById('dataChart').parentElement;
                    if (chartContainer) {
                        const loadingSpinner = document.createElement('div');
                        loadingSpinner.className = 'chart-loading-spinner';
                        loadingSpinner.style.position = 'absolute';
                        loadingSpinner.style.top = '50%';
                        loadingSpinner.style.left = '50%';
                        loadingSpinner.style.transform = 'translate(-50%, -50%)';
                        loadingSpinner.style.width = '40px';
                        loadingSpinner.style.height = '40px';
                        loadingSpinner.style.border = '3px solid rgba(0, 0, 0, 0.1)';
                        loadingSpinner.style.borderTopColor = '#2196f3';
                        loadingSpinner.style.borderRadius = '50%';
                        loadingSpinner.style.animation = 'spin 1s linear infinite';
                        
                        chartContainer.style.position = 'relative';
                        chartContainer.appendChild(loadingSpinner);
                        
                        // 1秒后移除加载指示器
                        setTimeout(() => {
                            if (loadingSpinner && loadingSpinner.parentNode) {
                                loadingSpinner.parentNode.removeChild(loadingSpinner);
                            }
                        }, 1000);
                    }
                    
                    // 获取当前选择的时间周期 - 检查元素是否存在
                    const activePeriodElement = document.querySelector('.period-option.active');
                    const period = activePeriodElement ? activePeriodElement.getAttribute('data-period') : '24h';
                    
                    // 销毁现有图表
                    if (window.dataChart && typeof window.dataChart.destroy === 'function') {
                        window.dataChart.destroy();
                        window.dataChart = null;
                    }
                    
                    try {
                        // 延迟更新图表
                        setTimeout(() => {
                            try {
                                // 使用固定的日期范围
                                const endDate = new Date();
                                // 默认使用7天时间范围
                                const startDate = new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000));
                                initDataChartWithDateRange(startDate, endDate, chartType);
                            } catch (e) {
                                console.error('切换图表类型出错:', e);
                            }
                        }, 300);
                    } catch (error) {
                        console.error('图表类型切换出错:', error);
                    }
                });
            });
            
            // 添加提示文本
            const viewToggle = document.querySelector('.view-toggle');
            if (viewToggle) {
                const tooltip = document.createElement('div');
                tooltip.style.position = 'absolute';
                tooltip.style.right = '0';
                tooltip.style.top = '-18px';
                tooltip.style.fontSize = '10px';
                tooltip.style.color = '#666';
                tooltip.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
                tooltip.style.padding = '2px 8px';
                tooltip.style.borderRadius = '10px';
                tooltip.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                tooltip.style.opacity = '0';
                tooltip.style.transition = 'opacity 0.3s ease';
                tooltip.textContent = '切换图表类型';
                
                viewToggle.parentNode.style.position = 'relative';
                viewToggle.parentNode.appendChild(tooltip);
                
                setTimeout(() => {
                    tooltip.style.opacity = '1';
                }, 2000);
                
                setTimeout(() => {
                    tooltip.style.opacity = '0';
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.parentNode.removeChild(tooltip);
                        }
                    }, 300);
                }, 6000);
            }
        }
        
        // 设置返回按钮
        function setupBackButton() {
            document.getElementById('backButton').addEventListener('click', () => {
                // 直接返回主页
                window.location.href = 'home.html';
            });
        }
        
        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', () => {
            try {
                console.log('页面初始化开始 - ' + new Date().toLocaleTimeString());
                
                // 确保首先引入key.js脚本
                if (typeof createCommonToken !== 'function') {
                    console.error('未检测到createCommonToken函数，确保key.js已正确加载');
                    // 动态加载key.js
                    const scriptElement = document.createElement('script');
                    scriptElement.src = 'key.js';
                    scriptElement.onload = () => {
                        console.log('key.js加载成功，继续初始化');
                        continueInitialization();
                    };
                    scriptElement.onerror = () => {
                        console.error('无法加载key.js');
                        // 继续初始化，但可能无法正常获取数据
                        continueInitialization();
                    };
                    document.head.appendChild(scriptElement);
                } else {
                    continueInitialization();
                }
            } catch (error) {
                console.error('页面初始化时出错:', error);
            }
        });
        
        // 继续初始化流程
        function continueInitialization() {
            console.log('继续初始化流程');
            
            // 确保所有设备都正确加载
            if (!allDevices || allDevices.length === 0) {
                console.error('设备列表为空！重新初始化设备列表');
                // 重新初始化设备列表
                const defaultDevices = [
                    {
                        type: 'pump',
                        name: '捕收剂阳',
                        status: '运行中',
                        icon: 'gear',
                        id: 'pump1'
                    },
                    {
                        type: 'pump',
                        name: '捕收剂阴',
                        status: '已停止',
                        icon: 'settings',
                        id: 'pump2'
                    },
                    {
                        type: 'pump',
                        name: '起泡剂',
                        status: '运行中',
                        icon: 'plus',
                        id: 'pump3'
                    },
                    {
                        type: 'pump',
                        name: '活化剂',
                        status: '已停止',
                        icon: 'star',
                        id: 'pump4'
                    },
                    {
                        type: 'pump',
                        name: '抑制剂',
                        status: '运行中',
                        icon: 'minus',
                        id: 'pump5'
                    },
                    {
                        type: 'pump',
                        name: '调整剂',
                        status: '已停止',
                        icon: 'refresh',
                        id: 'pump6'
                    },
                    {
                        type: 'sensor',
                        name: 'PH传感器',
                        status: '7.2',
                        icon: 'info',
                        id: 'ph'
                    },
                    {
                        type: 'sensor',
                        name: '水位传感器',
                        status: '80%',
                        icon: 'more',
                        id: 'water'
                    }
                ];
                
                // 恢复设备列表
                window.allDevices = defaultDevices;
            }
                
            // 隐藏全局加载指示器
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
            
            try {
                // 初始化UI组件
                initDateRangeSelector();
                setupChartToggle();
                setupBackButton();
                
                // 初始化设备选择器
                initDeviceSelector();
                
                // 从URL获取设备信息
                getDeviceFromUrl();
                
                // 初始隐藏数值历史记录和预测分析部分
                const numericHistorySection = document.getElementById('numericHistorySection');
                const predictionSection = document.getElementById('predictionSection');
                
                if (numericHistorySection && predictionSection) {
                    // 判断当前设备是否为传感器
                    const isSensorDevice = deviceData.id === 'ph' || deviceData.id === 'water';
                    
                    if (!isSensorDevice) {
                        numericHistorySection.style.display = 'none';
                        predictionSection.style.display = 'none';
                    }
                }
                
                // 初始化设备统计数据
                updateDeviceStatistics(deviceData);
                
                // 初始化数据显示 - 使用日期范围
                // 获取当前时间作为结束时间
                const endDate = new Date();
                // 默认显示最近5天数据
                const startDate = new Date(endDate.getTime() - 5 * 24 * 60 * 60 * 1000);
                
                // 强制初始化状态矩阵，确保显示数据
                setTimeout(() => {
                    try {
                        // 检查状态矩阵是否已经有内容
                        const statusMatrix = document.getElementById('statusMatrix');
                        if (!statusMatrix || statusMatrix.childElementCount === 0) {
                            console.log('状态矩阵为空，进行初始化');
                            initStatusMatrixWithDateRange(startDate, endDate);
                        } else {
                            console.log('状态矩阵已有内容，跳过初始化');
                        }
                        
                        // 初始化传感器设备图表
                        if (deviceData.id === 'ph' || deviceData.id === 'water') {
                            const chartType = document.querySelector('.toggle-option.active')?.getAttribute('data-view') || 'line';
                            initDataChartWithDateRange(startDate, endDate, chartType);
                            initPredictionChartWithDateRange(startDate, endDate);
                        }
                        
                        // 美化页面滚动效果
                        addScrollEffects();
                        
                        // 添加设备类型提示
                        addDeviceTypeIndicator();
                        
                        // 为设备添加刷新按钮
                        addDeviceRefreshButton();
                        
                        // 设置自动刷新
                        setupAutoRefresh();
                        
                        // 添加设备状态动态图标
                        setupDynamicDeviceIcons();
                        
                        // 页面可见性变化处理
                        setupVisibilityHandling();
                        
                        console.log('页面初始化完成 - ' + new Date().toLocaleTimeString());
                    } catch (error) {
                        console.error('初始化矩阵和图表时出错:', error);
                        // 尝试再次初始化
                        setTimeout(() => {
                            try {
                                const statusMatrix = document.getElementById('statusMatrix');
                                if (!statusMatrix || statusMatrix.childElementCount === 0) {
                                    initStatusMatrix('24h');
                                }
                            } catch (e) {
                                console.error('再次初始化失败:', e);
                            }
                        }, 1000);
                    }
                }, 500);
            } catch (error) {
                console.error('初始化过程中出错:', error);
            }
            
            // 页面初始化已在continueInitialization函数中完成
        }
        
        // 添加页面可见性变化处理
        function setupVisibilityHandling() {
            // 当页面变为可见时，立即刷新数据
            document.addEventListener('visibilitychange', () => {
                if (document.visibilityState === 'visible') {
                    // 页面重新变为可见时刷新数据
                    setTimeout(() => {
                        refreshData(); // 现在可以正确引用全局函数
                    }, 300);
                }
            });
            
            // 当页面从后台恢复时也刷新数据
            window.addEventListener('focus', () => {
                setTimeout(() => {
                    refreshData(); // 现在可以正确引用全局函数
                }, 300);
            });
        }
        
        // 设置动态设备图标
        function setupDynamicDeviceIcons() {
            // 每10秒更新一次设备图标状态
            setInterval(() => {
                const deviceIconElement = document.getElementById('deviceIcon');
                
                if (!deviceIconElement) return;
                
                if (['pump1', 'pump2', 'pump3', 'pump4', 'pump5', 'pump6'].includes(deviceData.id)) {
                    // 泵类设备图标动画
                    if (deviceData.status === '运行中') {
                        // 添加旋转动画
                        deviceIconElement.style.animation = 'spin 4s linear infinite';
                    } else {
                        // 停止旋转
                        deviceIconElement.style.animation = '';
                    }
                } else if (deviceData.id === 'ph') {
                    // PH传感器闪烁效果
                    deviceIconElement.style.animation = 'pulse 2s ease-in-out infinite';
                } else if (deviceData.id === 'water') {
                    // 水位传感器波浪效果
                    if (!deviceIconElement.querySelector('.water-wave')) {
                        const waterWave = document.createElement('div');
                        waterWave.className = 'water-wave';
                        waterWave.style.position = 'absolute';
                        waterWave.style.bottom = '0';
                        waterWave.style.left = '0';
                        waterWave.style.right = '0';
                        waterWave.style.height = '30%';
                        waterWave.style.backgroundColor = 'rgba(33, 150, 243, 0.3)';
                        waterWave.style.borderTopLeftRadius = '50%';
                        waterWave.style.borderTopRightRadius = '50%';
                        waterWave.style.animation = 'waterWave 2s ease-in-out infinite';
                        
                        // 添加水波动画
                        const style = document.createElement('style');
                        style.textContent = `
                            @keyframes waterWave {
                                0%, 100% { transform: translateY(0) scale(0.8); }
                                50% { transform: translateY(-5px) scale(1); }
                            }
                        `;
                        document.head.appendChild(style);
                        
                        deviceIconElement.style.position = 'relative';
                        deviceIconElement.style.overflow = 'hidden';
                        deviceIconElement.appendChild(waterWave);
                    }
                }
            }, 10000);
        }
        
        // 更新预测洞察文本
        function updatePredictionInsight(data, stats) {
            const insightElement = document.getElementById('predictionInsight');
            if (!insightElement) return;
            
            try {
                // 计算趋势
                const trendCalculation = () => {
                    // 简单线性回归趋势分析
                    const n = data.length;
                    const indices = Array.from({length: n}, (_, i) => i);
                    
                    // 计算x和y的平均值
                    const xMean = indices.reduce((sum, i) => sum + i, 0) / n;
                    const yMean = data.reduce((sum, y) => sum + y, 0) / n;
                    
                    // 计算斜率
                    let numerator = 0;
                    let denominator = 0;
                    for (let i = 0; i < n; i++) {
                        numerator += (indices[i] - xMean) * (data[i] - yMean);
                        denominator += Math.pow(indices[i] - xMean, 2);
                    }
                    
                    const slope = denominator !== 0 ? numerator / denominator : 0;
                    return slope;
                };
                
                const trend = trendCalculation();
                const trendThreshold = 0.05;  // 趋势判断阈值
                
                let trendText, adviceText, stabilityText;
                
                // 判断趋势
                if (trend > trendThreshold) {
                    trendText = "上升";
                } else if (trend < -trendThreshold) {
                    trendText = "下降";
                } else {
                    trendText = "保持稳定";
                }
                
                // 判断波动性/稳定性
                const variability = parseFloat(stats.std) / parseFloat(stats.avg);
                if (variability > 0.1) {
                    stabilityText = "波动较大";
                } else if (variability > 0.05) {
                    stabilityText = "有一定波动";
                } else {
                    stabilityText = "运行稳定";
                }
                
                // 生成建议
                if (deviceData.id === 'ph') {
                    if (parseFloat(stats.avg) > 7.5) {
                        adviceText = "PH值偏高，建议适当调整水质。";
                    } else if (parseFloat(stats.avg) < 6.5) {
                        adviceText = "PH值偏低，建议检查水质调节装置。";
                    } else {
                        adviceText = "PH值处于正常范围，建议保持当前调控参数。";
                    }
                } else if (deviceData.id === 'water') {
                    if (parseFloat(stats.avg) < 75) {
                        adviceText = "水位较低，建议检查补水系统。";
                    } else if (trend < -trendThreshold) {
                        adviceText = "水位呈下降趋势，建议关注后续变化并准备调整。";
                    } else {
                        adviceText = "水位处于正常范围，建议定期检查设备状态。";
                    }
                } else {
                    // 泵类设备
                    if (trend > trendThreshold) {
                        adviceText = "设备运行频率增加，请注意防止过载。";
                    } else if (variability > 0.1) {
                        adviceText = "设备运行不稳定，建议进行维护检查。";
                    } else {
                        adviceText = "设备运行正常，建议按计划进行维护。";
                    }
                }
                
                // 组合洞察文本
                const insightText = `根据历史数据分析，该设备运行${stabilityText}，趋势${trendText}。${adviceText}`;
                insightElement.textContent = insightText;
            } catch (error) {
                console.error('更新预测洞察文本时出错:', error);
                insightElement.textContent = '数据分析中...';
            }
        }
        
        // 添加滚动动画效果
        function addScrollEffects() {
            const historyContent = document.querySelector('.history-content');
            
            // 添加滚动阴影效果
            historyContent.addEventListener('scroll', () => {
                if (historyContent.scrollTop > 10) {
                    historyContent.style.boxShadow = '0 -8px 20px rgba(0, 0, 0, 0.1)';
                } else {
                    historyContent.style.boxShadow = '0 -4px 10px rgba(0, 0, 0, 0.05)';
                }
            });
            
            // 添加图表加载动画
            const charts = document.querySelectorAll('.chart-container');
            charts.forEach(chart => {
                chart.style.opacity = '0';
                chart.style.transform = 'translateY(20px)';
                chart.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                
                setTimeout(() => {
                    chart.style.opacity = '1';
                    chart.style.transform = 'translateY(0)';
                }, 300);
            });
            
            // 添加刷新统计数据按钮功能
            const refreshStats = document.getElementById('refreshStats');
            if (refreshStats) {
                refreshStats.addEventListener('click', function() {
                    const rotateAnimation = [
                        { transform: 'rotate(0deg)' },
                        { transform: 'rotate(360deg)' }
                    ];
                    
                    const rotateOptions = {
                        duration: 500,
                        iterations: 1
                    };
                    
                    this.querySelector('svg').animate(rotateAnimation, rotateOptions);
                    
                    // 使用设备统计更新函数更新当前设备的统计数据
                    updateDeviceStatistics(deviceData);
                });
            }
        }
        
        // 显示加载指示器 - 改为每个设备单独显示加载指示器
        function showLoading() {
            // 不再使用全局加载指示器
            /*
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.classList.add('show');
            }
            
            // 设置超时确保加载指示器不会无限显示
            if (window.loadingTimeout) {
                clearTimeout(window.loadingTimeout);
            }
            window.loadingTimeout = setTimeout(hideLoading, 3000);
            */
        }
        
        // 隐藏加载指示器
        function hideLoading() {
            // 不再使用全局加载指示器
            /*
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.classList.remove('show');
            }
            
            if (window.loadingTimeout) {
                clearTimeout(window.loadingTimeout);
                window.loadingTimeout = null;
            }
            */
        }
        
        // 添加设备选择器滚动指引
        function addScrollGuide() {
            // 检查是否已有指引元素
            if (document.querySelector('.scroll-guide')) {
                return;
            }
            
            const deviceSelector = document.getElementById('deviceSelector');
            const selectorContainer = document.querySelector('.device-selector-container');
            
            // 检查是否需要滚动指引（内容是否溢出）
            if (deviceSelector.scrollWidth <= deviceSelector.clientWidth) {
                return;
            }
            
            // 创建右侧滚动指引
            const rightGuide = document.createElement('div');
            rightGuide.className = 'scroll-guide pulse-animation';
            rightGuide.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
            `;
            
            // 点击滚动到下一组设备
            rightGuide.addEventListener('click', () => {
                deviceSelector.scrollBy({
                    left: 150,
                    behavior: 'smooth'
                });
            });
            
            // 监听滚动事件，到达末尾时隐藏指引
            deviceSelector.addEventListener('scroll', () => {
                if (deviceSelector.scrollLeft + deviceSelector.clientWidth >= deviceSelector.scrollWidth - 10) {
                    rightGuide.style.opacity = '0';
                    setTimeout(() => {
                        rightGuide.remove();
                    }, 300);
                }
            });
            
            // 添加到DOM
            selectorContainer.appendChild(rightGuide);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                rightGuide.classList.remove('pulse-animation');
                rightGuide.style.opacity = '0.3';
            }, 3000);
        }
        
        // 添加设备类型指示器
        function addDeviceTypeIndicator() {
            const deviceInfoCard = document.querySelector('.device-info-card');
            if (!deviceInfoCard) return;
            
            // 移除已存在的指示器
            const existingIndicator = document.getElementById('device-type-indicator');
            if (existingIndicator) {
                existingIndicator.remove();
            }
            
            // 创建指示器
            const indicator = document.createElement('div');
            indicator.id = 'device-type-indicator';
            indicator.style.position = 'absolute';
            indicator.style.top = '10px';
            indicator.style.right = '10px';
            indicator.style.fontSize = '11px';
            indicator.style.padding = '3px 8px';
            indicator.style.borderRadius = '10px';
            indicator.style.fontWeight = '500';
            
            // 根据设备类型设置不同样式
            if (deviceData.type === 'sensor') {
                indicator.textContent = '传感器';
                indicator.style.backgroundColor = 'rgba(33, 150, 243, 0.1)';
                indicator.style.color = '#2196f3';
                indicator.style.border = '1px solid rgba(33, 150, 243, 0.2)';
            } else {
                indicator.textContent = '泵设备';
                indicator.style.backgroundColor = 'rgba(76, 175, 80, 0.1)';
                indicator.style.color = '#4caf50';
                indicator.style.border = '1px solid rgba(76, 175, 80, 0.2)';
            }
            
            deviceInfoCard.style.position = 'relative';
            deviceInfoCard.appendChild(indicator);
        }
        
        // 添加设备刷新按钮
        function addDeviceRefreshButton() {
            const deviceInfoCard = document.querySelector('.device-info-card');
            if (!deviceInfoCard) return;
            
            // 移除已存在的刷新按钮
            const existingRefresh = document.getElementById('device-refresh-btn');
            if (existingRefresh) {
                existingRefresh.remove();
            }
            
            // 创建刷新按钮
            const refreshButton = document.createElement('div');
            refreshButton.id = 'device-refresh-btn';
            refreshButton.style.position = 'absolute';
            refreshButton.style.bottom = '10px';
            refreshButton.style.right = '10px';
            refreshButton.style.width = '30px';
            refreshButton.style.height = '30px';
            refreshButton.style.borderRadius = '50%';
            refreshButton.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
            refreshButton.style.display = 'flex';
            refreshButton.style.justifyContent = 'center';
            refreshButton.style.alignItems = 'center';
            refreshButton.style.cursor = 'pointer';
            refreshButton.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.1)';
            refreshButton.style.transition = 'all 0.3s ease';
            refreshButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M23 4v6h-6"></path>
                    <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
                </svg>
            `;
            refreshButton.title = '刷新设备数据';
            
            // 悬停效果
            refreshButton.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(33, 150, 243, 0.1)';
                this.style.transform = 'scale(1.1)';
            });
            
            refreshButton.addEventListener('mouseleave', function() {
                this.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
                this.style.transform = 'scale(1)';
            });
            
            // 点击事件
            refreshButton.addEventListener('click', function() {
                // 旋转动画
                const rotateAnimation = [
                    { transform: 'rotate(0deg)' },
                    { transform: 'rotate(360deg)' }
                ];
                
                this.querySelector('svg').animate(rotateAnimation, {
                    duration: 800,
                    iterations: 1
                });
                
                // 刷新当前设备数据
                setTimeout(() => {
                    const deviceId = deviceData.id;
                    changeDevice(deviceId);
                }, 300);
            });
            
            deviceInfoCard.style.position = 'relative';
            deviceInfoCard.appendChild(refreshButton);
        }
        
        // 设置自动刷新功能
        function setupAutoRefresh() {
            // 立即执行一次数据刷新，确保初始数据显示
            setTimeout(() => {
                refreshData();
                console.log('初始数据刷新完成');
                
                // 设置30秒自动刷新
                setInterval(() => {
                    console.log('自动刷新数据');
                    refreshData();
                    showToast('数据已自动刷新', 'info');
                }, 30000); // 30秒刷新一次
            }, 1000);
        }
        
        // 刷新数据的函数 - 移至全局作用域
        function refreshData() {
            console.log('正在刷新数据...');
            
            // 获取当前选择的设备
            const currentDeviceId = deviceData.id;
            
            // 获取当前日期选择器的日期范围
            const dateRange = $('#dateRangePicker').val();
            let startDate, endDate;
            
            if (dateRange) {
                const [startStr, endStr] = dateRange.split(' - ');
                startDate = moment(startStr, 'YYYY-MM-DD HH:mm:ss').toDate();
                endDate = moment(endStr, 'YYYY-MM-DD HH:mm:ss').toDate();
            } else {
                // 默认使用最近5天
                endDate = new Date();
                startDate = new Date(endDate.getTime() - 5 * 24 * 60 * 60 * 1000);
            }
            
            // 使用改进后的fetchHistoricalData函数
            if (typeof window.fetchHistoricalData === 'function') {
                console.log('使用fetchHistoricalData API获取数据');
                
                // 准备查询参数 - 使用新的API参数格式
                const queryParams = {
                    productId: 'HTJ98Pjh4a',
                    deviceName: 'flotation',
                    accessKey: '****************************************************************',
                    // 根据设备类型设置正确的功能点标识符
                    datastreamId: getCorrectDatastreamId(deviceData.id),
                    // 默认限制100条数据
                    limit: 100
                };
                
                // 设置毫秒时间戳
                queryParams.end = endDate.getTime();
                queryParams.start = startDate.getTime();
                
                // 调用API
                fetchHistoricalData(queryParams)
                    .then(data => {
                        console.log('历史数据获取成功:', data);
                        if (data && data.code === 0) {
                            // 检查数据结构
                            if (!data.data || !data.data.datastreams || !Array.isArray(data.data.datastreams) || data.data.datastreams.length === 0) {
                                console.log('API返回的数据中没有datastreams或为空数组，尝试创建默认结构');
                                
                                // 创建默认数据结构
                                if (!data.data) data.data = {};
                                if (!data.data.datastreams) data.data.datastreams = [];
                                
                                // 添加空的datastream
                                if (data.data.datastreams.length === 0) {
                                    data.data.datastreams.push({
                                        id: getCorrectDatastreamId(deviceData.id),
                                        datapoints: []
                                    });
                                }
                            }
                            
                            // 更新当前视图
                            processHistoricalData(data);
                            console.log('数据刷新成功');
                        } else {
                            console.error('API返回错误:', data);
                            throw new Error(`API错误: ${data.msg || '未知错误'}`);
                        }
                    })
                    .catch(error => {
                        console.error('获取历史数据失败:', error);
                        showRefreshError();
                        // 使用备用方法
                        fallbackToDeviceStatus();
                    });
            } else {
                console.warn('fetchHistoricalData API不可用，使用设备状态API');
                fallbackToDeviceStatus();
            }
            
            // 备用方法：获取设备当前状态
            function fallbackToDeviceStatus() {
                try {
                    // 创建token
                    const params = {
                        author_key: '****************************************************************',
                        version: '2022-05-01',
                        user_id: '420568',
                    };
                    
                    // 使用token访问API获取当前设备状态
                    const token = createCommonToken(params);
                    
                    // 查询设备状态
                    const url = `https://iot-api.heclouds.com/thingmodel/query-device-property?product_id=HTJ98Pjh4a&device_name=flotation`;
                    
                    fetch(url, {
                        method: 'GET',
                        headers: {
                            'authorization': token
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`API请求失败: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data && data.data) {
                            // 更新当前视图
                            updateCurrentView(data);
                            console.log('设备状态数据刷新成功');
                        } else {
                            console.error('API返回数据格式不正确:', data);
                            showRefreshError();
                        }
                    })
                    .catch(error => {
                        console.error('刷新数据失败:', error);
                        showRefreshError();
                    });
                } catch (error) {
                    console.error('创建token失败:', error);
                    showRefreshError();
                }
            }
            
                                        // 处理历史数据
            function processHistoricalData(data) {
                console.log('处理历史数据:', data);
                
                // 检查数据格式是否正确
                if (!data || !data.data) {
                    console.error('历史数据格式不正确: 缺少data字段');
                    return;
                }
                
                // 检查是否使用新API格式（data.data.list）
                if (data.data.list && Array.isArray(data.data.list)) {
                    console.log('检测到新API格式，转换为兼容格式');
                    
                    // 将新API格式转换为兼容旧代码的格式
                    const compatibleData = {
                        code: data.code,
                        msg: data.msg,
                        request_id: data.request_id,
                        data: {
                            datastreams: [
                                {
                                    id: getCorrectDatastreamId(deviceData.id),
                                    datapoints: data.data.list.map(item => ({
                                        at: new Date(parseInt(item.time)).toISOString(),
                                        value: item.value
                                    }))
                                }
                            ]
                        }
                    };
                    
                    // 替换data为兼容格式
                    data = compatibleData;
                    console.log('已转换为兼容格式:', data);
                }
                // 检查是否有datastreams数组，如果没有则创建默认结构
                else if (!data.data.datastreams || !Array.isArray(data.data.datastreams) || data.data.datastreams.length === 0) {
                    console.log('历史数据中没有datastreams或为空数组，创建默认结构');
                    
                    if (!data.data.datastreams) {
                        data.data.datastreams = [];
                    }
                    
                    // 添加默认的datastream
                    data.data.datastreams.push({
                        id: getCorrectDatastreamId(deviceData.id),
                        datapoints: []
                    });
                }
                
                // 更新图表和状态矩阵
                try {
                    const datastream = data.data.datastreams[0];
                    
                    // 检查datapoints是否存在，如果不存在则创建空数组
                    if (!datastream.datapoints) {
                        console.log('历史数据中没有datapoints字段，创建空数组');
                        datastream.datapoints = [];
                    } else if (!Array.isArray(datastream.datapoints)) {
                        console.log('历史数据中datapoints不是数组，转换为空数组');
                        datastream.datapoints = [];
                    }
                    
                    const datapoints = datastream.datapoints;
                    
                    // 即使datapoints为空，也继续处理，显示为无数据状态
                    console.log(`获取到${datapoints.length}个数据点`);
                    
                    // 获取当前日期选择器的时间范围
                    const dateRange = $('#dateRangePicker').val();
                    let startDate, endDate;
                    
                    if (dateRange) {
                        const [startStr, endStr] = dateRange.split(' - ');
                        startDate = moment(startStr, 'YYYY-MM-DD HH:mm:ss').toDate();
                        endDate = moment(endStr, 'YYYY-MM-DD HH:mm:ss').toDate();
                    } else {
                        // 默认使用最近5天
                        endDate = new Date();
                        startDate = new Date(endDate.getTime() - 5 * 24 * 60 * 60 * 1000);
                    }
                    
                    // 转换数据点格式
                    const chartData = datapoints.map(point => {
                        // 确保at和value字段存在
                        if (!point.at || point.value === undefined) {
                            console.warn('数据点缺少at或value字段:', point);
                            return null;
                        }
                        
                        // 尝试解析日期和值
                        try {
                            const date = new Date(point.at);
                            const value = parseFloat(point.value);
                            
                            // 检查解析结果是否有效
                            if (isNaN(date.getTime()) || isNaN(value)) {
                                console.warn('无效的日期或值:', point.at, point.value);
                                return null;
                            }
                            
                            return {
                                x: date,
                                y: value
                            };
                        } catch (err) {
                            console.warn('解析数据点出错:', err, point);
                            return null;
                        }
                    }).filter(point => point !== null); // 过滤掉无效的数据点
                    
                    if (chartData.length === 0) {
                        console.warn('没有有效的数据点可以显示');
                        return;
                    }
                    
                    console.log('有效数据点:', chartData.length);
                    
                    // 使用日期范围重新初始化图表和矩阵
                    if (deviceData.id === 'ph' || deviceData.id === 'water') {
                        // 获取当前图表类型
                        const chartType = document.querySelector('.toggle-option.active')?.getAttribute('data-view') || 'line';
                        
                        // 使用日期范围更新图表
                        initDataChartWithDateRange(startDate, endDate, chartType);
                        
                        // 更新预测图表
                        initPredictionChartWithDateRange(startDate, endDate);
                        
                        // 更新统计数据
                            try {
                                const values = chartData.map(point => point.y);
                                // 判断全局作用域是否存在updateDataStatistics函数
                                if (typeof window.updateDataStatistics === 'function') {
                                    window.updateDataStatistics(values, true);
                                    console.log('统计数据已更新');
                                } else {
                                    console.warn('updateDataStatistics函数不可用，尝试使用updateChartStatistics');
                                    // 尝试使用其他可能存在的统计函数
                                    if (typeof updateChartStatistics === 'function') {
                                        updateChartStatistics(values);
                                    } else {
                                        // 直接更新统计元素
                                        updateStatsDirectly(values);
                                    }
                                }
                            } catch (error) {
                                console.error('更新统计数据失败:', error);
                        }
                    }
                    
                    // 更新状态矩阵
                    initStatusMatrixWithDateRange(startDate, endDate);
                    
                    // 更新当前设备状态
                    if (datapoints.length > 0) {
                        const latestPoint = datapoints[datapoints.length - 1];
                        updateDeviceCurrentStatus(latestPoint.value);
                    }
                } catch (error) {
                    console.error('处理历史数据时出错:', error);
                }
            }
            
            // 更新设备当前状态 - 定义为全局函数
            window.updateDeviceCurrentStatus = function(value) {
                const deviceStatusElement = document.getElementById('deviceStatus');
                if (deviceStatusElement) {
                    deviceStatusElement.textContent = value;
                    deviceStatusElement.style.animation = 'none';
                    setTimeout(() => {
                        deviceStatusElement.style.animation = 'pulse 0.5s ease-in-out';
                    }, 10);
                }
            }
            
            // 为兼容性提供全局访问
            updateDeviceCurrentStatus = window.updateDeviceCurrentStatus;
        }
        
        // 更新当前视图
        function updateCurrentView(apiData) {
            // 根据当前视图类型和选择的设备更新界面
            const currentDeviceId = deviceData.id;
            
            // 获取当前日期选择器的时间范围
            const dateRange = $('#dateRangePicker').val();
            let startDate, endDate;
            
            if (dateRange) {
                const [startStr, endStr] = dateRange.split(' - ');
                startDate = moment(startStr, 'YYYY-MM-DD HH:mm:ss').toDate();
                endDate = moment(endStr, 'YYYY-MM-DD HH:mm:ss').toDate();
            } else {
                // 默认使用最近5天
                endDate = new Date();
                startDate = new Date(endDate.getTime() - 5 * 24 * 60 * 60 * 1000);
            }
            
            // 找到对应的设备数据在API返回中的索引
            let dataIndex = -1;
            
            if (currentDeviceId === 'ph') {
                dataIndex = 0; // PH值在数据中的索引
            } else if (currentDeviceId === 'water') {
                dataIndex = 10; // 水位在数据中的索引
            } else if (currentDeviceId === 'pump1') {
                dataIndex = 1;
            } else if (currentDeviceId === 'pump2') {
                dataIndex = 2;
            } else if (currentDeviceId === 'pump3') {
                dataIndex = 3;
            } else if (currentDeviceId === 'pump4') {
                dataIndex = 4;
            } else if (currentDeviceId === 'pump5') {
                dataIndex = 5;
            } else if (currentDeviceId === 'pump6') {
                dataIndex = 6;
            }
            
            if (dataIndex !== -1 && apiData.data[dataIndex]) {
                // 更新设备当前状态
                const deviceValue = apiData.data[dataIndex].value;
                
                // 更新设备信息卡片
                const statusElement = document.querySelector('.device-info-status');
                if (statusElement) {
                    if (['pump1', 'pump2', 'pump3', 'pump4', 'pump5', 'pump6'].includes(currentDeviceId)) {
                        // 泵设备
                        const isRunning = deviceValue === 'true';
                        statusElement.textContent = isRunning ? '运行中' : '已停止';
                        statusElement.className = `device-info-status ${isRunning ? 'active' : 'inactive'}`;
                    } else {
                        // 传感器
                        statusElement.textContent = deviceValue;
                        statusElement.className = 'device-info-status sensor';
                    }
                }
                
                // 如果是状态矩阵视图，刷新矩阵
                if (document.querySelector('#booleanHistorySection').style.display !== 'none') {
                    initStatusMatrixWithDateRange(startDate, endDate);
                }
                
                // 如果是图表视图，刷新图表
                if (document.querySelector('#numericHistorySection').style.display !== 'none') {
                    const chartType = document.querySelector('.toggle-option.active')?.getAttribute('data-view') || 'line';
                    initDataChartWithDateRange(startDate, endDate, chartType);
                    
                    // 确保PH和水位传感器的统计数据更新
                    if (currentDeviceId === 'ph' || currentDeviceId === 'water') {
                        // 如果图表已有数据，直接从图表获取数据更新统计信息
                        if (window.dataChart && window.dataChart.data && window.dataChart.data.datasets[0].data) {
                            const chartValues = window.dataChart.data.datasets[0].data.map(point => point.y);
                            if (chartValues.length > 0) {
                                try {
                                    // 尝试使用全局函数更新统计
                                    if (typeof window.updateDataStatistics === 'function') {
                                        window.updateDataStatistics(chartValues, true);
                                        console.log('从图表更新统计数据');
                                    } else if (typeof updateStatsDirectly === 'function') {
                                        // 使用直接更新函数
                                        updateStatsDirectly(chartValues);
                                        console.log('直接从图表更新统计数据');
                                    }
                                } catch (error) {
                                    console.error('更新统计数据时出错:', error);
                                }
                            }
                        }
                    }
                }
                
                // 如果是预测视图，刷新预测
                if (document.querySelector('#predictionSection').style.display !== 'none') {
                    initPredictionChartWithDateRange(startDate, endDate);
                }
                
                // 显示刷新成功提示
                showRefreshSuccess();
            }
        }
        
        // 显示刷新成功提示
        function showRefreshSuccess() {
            showToast('数据已更新', 'success');
        }
        
                // 显示刷新失败提示
        function showRefreshError() {
            showToast('数据更新失败', 'error');
        }
        
        // 初始化日期范围选择器
        function initDateRangeSelector() {
            console.log('初始化日期范围选择器');
            
            // 确保选择器存在
            const selectorContainer = document.getElementById('dateRangeSelector');
            if (!selectorContainer) {
                console.error('找不到日期范围选择器容器');
                return;
            }
            
            // 确保jQuery已加载
            if (typeof jQuery === 'undefined') {
                console.error('jQuery未加载，无法初始化日期选择器');
                return;
            }
            
            // 初始化日期范围选择器
            const dateRangePicker = $('#dateRangePicker');
            if (dateRangePicker.length === 0) {
                console.error('找不到日期范围选择器输入框');
                return;
            }
            
            // 获取当前时间和7天前的时间作为默认值，起始时间为00:00:00，结束时间为23:59:59
            const endDate = moment().endOf('day'); // 23:59:59
            const startDate = moment().subtract(7, 'days').startOf('day'); // 00:00:00
            
            // 初始化日期选择器
            dateRangePicker.daterangepicker({
                startDate: startDate,
                endDate: endDate,
                opens: 'center',
                autoApply: true,
                locale: {
                    format: 'YYYY-MM-DD',
                    separator: ' - ',
                    applyLabel: '确定',
                    cancelLabel: '取消',
                    fromLabel: '从',
                    toLabel: '到',
                    customRangeLabel: '自定义范围',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    firstDay: 1
                },
                timePicker: false,
                timePicker24Hour: false,
                timePickerSeconds: false,
                maxSpan: { // 限制最大选择7天
                    days: 7
                },
                showDropdowns: true, // 显示年月选择器
                showWeekNumbers: false,
                showISOWeekNumbers: false,
                showCustomRangeLabel: true,
                ranges: {
                   '最近1小时': [moment().subtract(1, 'hours'), moment()],
                   '最近24小时': [moment().subtract(24, 'hours'), moment()],
                   '最近7天': [moment().subtract(7, 'days').startOf('day'), moment().endOf('day')],
                   '本周': [moment().startOf('week'), moment().endOf('day')],
                   '本月': [moment().startOf('month'), moment().endOf('day')],
                   '上个月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            });
            
            // 添加样式优化
            setTimeout(() => {
                $('.daterangepicker').addClass('custom-daterangepicker');
                
                // 添加自定义样式
                if (!document.getElementById('daterangepicker-custom-style')) {
                    const customStyle = document.createElement('style');
                    customStyle.id = 'daterangepicker-custom-style';
                    customStyle.textContent = `
                        .custom-daterangepicker {
                            border-radius: 8px;
                            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                            border: none;
                            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                            z-index: 9999 !important;
                        }
                        .custom-daterangepicker .ranges {
                            padding: 10px;
                            background: #f8f9fa;
                            border-radius: 8px 0 0 8px;
                            width: 180px;
                        }
                        .custom-daterangepicker .ranges ul {
                            width: 100%;
                        }
                        .custom-daterangepicker .ranges ul li {
                            padding: 8px 12px;
                            border-radius: 4px;
                            transition: all 0.2s;
                            margin-bottom: 5px;
                        }
                        .custom-daterangepicker .ranges ul li:hover {
                            background: #e9ecef;
                        }
                        .custom-daterangepicker .ranges ul li.active {
                            background: #2196f3;
                            color: white;
                        }
                        .custom-daterangepicker .drp-calendar {
                            border-radius: 0 8px 8px 0;
                            padding: 10px;
                        }
                        .custom-daterangepicker .calendar-table {
                            border-radius: 4px;
                            overflow: hidden;
                        }
                        .custom-daterangepicker th.month {
                            font-weight: 600;
                            color: #333;
                        }
                        .custom-daterangepicker td.active, 
                        .custom-daterangepicker td.active:hover {
                            background-color: #2196f3;
                        }
                        .custom-daterangepicker td.in-range {
                            background-color: rgba(33, 150, 243, 0.2);
                        }
                        .custom-daterangepicker .drp-buttons {
                            padding: 10px;
                            border-top: 1px solid #eee;
                        }
                        .custom-daterangepicker .drp-selected {
                            font-size: 12px;
                            padding: 5px;
                        }
                        /* 确保日期选择器不被遮挡 */
                        .daterangepicker {
                            position: fixed !important; 
                            top: 50% !important;
                            left: 50% !important;
                            transform: translate(-50%, -50%) !important;
                        }
                    `;
                    document.head.appendChild(customStyle);
                }
            }, 500);
            
            // 设置初始值
            dateRangePicker.val(startDate.format('YYYY-MM-DD') + ' - ' + endDate.format('YYYY-MM-DD'));
            
            // 添加日期范围验证和格式化
            dateRangePicker.on('apply.daterangepicker', function(ev, picker) {
                // 计算选择的日期范围天数
                const diff = picker.endDate.diff(picker.startDate, 'days');
                
                // 如果超过7天，提示用户并重置为7天
                if (diff > 7) {
                    showToast('时间范围不能超过7天，已自动调整', 'warning');
                    const newStartDate = moment(picker.endDate).subtract(7, 'days').startOf('day');
                    picker.setStartDate(newStartDate);
                }
                
                // 确保开始时间是当天的00:00:00
                picker.setStartDate(picker.startDate.clone().startOf('day'));
                // 确保结束时间是当天的23:59:59
                picker.setEndDate(picker.endDate.clone().endOf('day'));
                
                // 更新输入框显示
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
            });
            
            // 添加查询按钮事件监听
            const searchBtn = document.getElementById('searchDataBtn');
            if (searchBtn) {
                searchBtn.addEventListener('click', function() {
                    const dateRange = dateRangePicker.val();
                    const [startStr, endStr] = dateRange.split(' - ');
                    const start = moment(startStr, 'YYYY-MM-DD').startOf('day'); // 设置为当天开始时间
                    const end = moment(endStr, 'YYYY-MM-DD').endOf('day'); // 设置为当天结束时间
                    
                    if (!start.isValid() || !end.isValid()) {
                        showToast('日期格式无效，请重新选择', 'error');
                        return;
                    }
                    
                    // 显示加载提示
                    showToast('正在查询数据...');
                    
                    // 加载数据
                    loadHistoricalData(start.toDate(), end.toDate());
                });
            }
            
            // 更新快速选项，只保留最近1小时、最近24小时和最近7天
            // 首先清空现有选项
            const quickOptionsContainer = selectorContainer.querySelector('.quick-options');
            if (quickOptionsContainer) {
                // 添加样式使选项更美观
                if (!document.getElementById('quick-options-style')) {
                    const quickOptionsStyle = document.createElement('style');
                    quickOptionsStyle.id = 'quick-options-style';
                    quickOptionsStyle.textContent = `
                        .quick-options {
                            display: flex;
                            justify-content: center;
                            margin-top: 15px;
                            gap: 10px;
                        }
                        .quick-option {
                            padding: 8px 16px;
                            border-radius: 20px;
                            background-color: #f5f5f5;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                            text-align: center;
                            font-size: 14px;
                            color: #333;
                        }
                        .quick-option:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                            background-color: #e9ecef;
                        }
                        .quick-option.active {
                            background-color: #2196f3;
                            color: white;
                            box-shadow: 0 2px 5px rgba(33, 150, 243, 0.3);
                        }
                    `;
                    document.head.appendChild(quickOptionsStyle);
                }
                
                quickOptionsContainer.innerHTML = `
                    <div class="quick-option" data-range="1h">最近1小时</div>
                    <div class="quick-option" data-range="24h">最近24小时</div>
                    <div class="quick-option" data-range="7d">最近7天</div>
                `;
            }
            
            // 快速选项处理
            const quickOptions = selectorContainer.querySelectorAll('.quick-option');
            if (quickOptions && quickOptions.length > 0) {
                console.log(`找到${quickOptions.length}个快速选项`);
                
                quickOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        // 如果已经是活动的，不执行任何操作
                        if (this.classList.contains('active')) {
                            return;
                        }
                        
                        // 更新选中状态
                        quickOptions.forEach(opt => {
                            opt.classList.remove('active');
                        });
                        this.classList.add('active');
                        
                        // 获取选择的时间范围
                        const range = this.getAttribute('data-range');
                        const end = moment().endOf('day'); // 设置为当天23:59:59
                        let start;
                        
                        switch(range) {
                            case '1h':
                                start = moment().subtract(1, 'hours');
                                break;
                            case '24h':
                                start = moment().subtract(24, 'hours');
                                break;
                            case '7d':
                                start = moment().subtract(7, 'days').startOf('day'); // 设置为开始日00:00:00
                                break;
                            default:
                                start = moment().subtract(1, 'days').startOf('day');
                        }
                        
                        // 更新日期选择器
                        dateRangePicker.data('daterangepicker').setStartDate(start);
                        dateRangePicker.data('daterangepicker').setEndDate(end);
                        dateRangePicker.val(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                        
                        // 自动触发查询
                        searchBtn.click();
                    });
            });
            
            // 添加鼠标悬停效果
                quickOptions.forEach(option => {
                option.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'translateY(-2px)';
                            this.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.1)';
                    }
                });
                
                option.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = '';
                        this.style.boxShadow = '';
                    }
                });
            });
            
                // 默认选择"最近7天"
                const defaultOption = document.querySelector('.quick-option[data-range="7d"]');
                if (defaultOption && !defaultOption.classList.contains('active')) {
                    defaultOption.click();
                }
            }
            
            console.log('日期范围选择器初始化完成');
        }
        
        // 根据选择的时间范围加载历史数据
        function loadHistoricalData(startDate, endDate) {
            console.log(`加载历史数据: ${startDate.toLocaleString()} - ${endDate.toLocaleString()}`);
                
                // 显示加载指示器
                const loadingIndicator = document.getElementById('loadingIndicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'flex';
                }
                
            // 重置图表
            if (window.dataChart && typeof window.dataChart.destroy === 'function') {
                window.dataChart.destroy();
                window.dataChart = null;
            }
            
            // 获取图表类型
            const chartType = document.querySelector('.toggle-option.active')?.getAttribute('data-view') || 'line';
            
            try {
                // 确保传感器设备的图表和预测部分可见
                        if (deviceData.id === 'ph' || deviceData.id === 'water') {
                    // 确保图表部分可见
                    const numericHistorySection = document.getElementById('numericHistorySection');
                    if (numericHistorySection) {
                        numericHistorySection.style.display = 'block';
                    }
                    
                    // 确保预测部分可见
                    const predictionSection = document.getElementById('predictionSection');
                    if (predictionSection) {
                        predictionSection.style.display = 'block';
                    }
                    
                    // 初始化数据图表
                    initDataChartWithDateRange(startDate, endDate, chartType);
                    
                    // 初始化预测图表
                    initPredictionChartWithDateRange(startDate, endDate);
                }
                
                // 更新状态矩阵
                initStatusMatrixWithDateRange(startDate, endDate);
                        
                        // 隐藏加载指示器
                            setTimeout(() => {
                    if (loadingIndicator) {
                                loadingIndicator.style.display = 'none';
                        }
                    showToast('数据加载完成', 'success');
                }, 500);
                    } catch (error) {
                console.error('加载历史数据失败:', error);
                
                        // 隐藏加载指示器
                        if (loadingIndicator) {
                            loadingIndicator.style.display = 'none';
                        }
                
                showToast('加载数据失败: ' + error.message, 'error');
            }
        }
        // 根据日期范围初始化状态矩阵
        function initStatusMatrixWithDateRange(startDate, endDate) {
            console.log(`初始化状态矩阵，开始: ${startDate.toLocaleString()}, 结束: ${endDate.toLocaleString()}`);
            
            // 清空现有矩阵内容
            const matrixContainer = document.getElementById('statusMatrix');
            if (matrixContainer) {
                console.log('矩阵内容已清空，准备重新生成');
                matrixContainer.innerHTML = '';
            } else {
                console.error('找不到状态矩阵容器');
                return;
            }
            
            // 计算日期范围内的时间段数量
            const diffMs = endDate.getTime() - startDate.getTime();
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            
            // 确定每个单元格代表的时间间隔和总单元格数
            let cellCount, columnsCount;
            if (diffHours <= 24) { // 少于等于24小时，每小时一个单元格
                cellCount = diffHours || 24; // 至少显示24个单元格
                columnsCount = 8;
            } else if (diffHours <= 72) { // 少于等于3天，每3小时一个单元格
                cellCount = Math.ceil(diffHours / 3);
                columnsCount = 8;
            } else if (diffHours <= 120) { // 少于等于5天，每5小时一个单元格
                cellCount = Math.ceil(diffHours / 5);
                columnsCount = 8;
            } else { // 大于5天，每6小时一个单元格
                cellCount = Math.ceil(diffHours / 6);
                columnsCount = 7;
            }
            
            // 生成单元格并添加到矩阵中
            console.log(`开始创建${cellCount}个矩阵单元格，列数：${columnsCount}`);
            
            const fragment = document.createDocumentFragment();
            const intervalMs = diffMs / cellCount;
            
            for (let i = 0; i < cellCount; i++) {
                const cell = document.createElement('div');
                cell.className = 'matrix-cell';
                cell.setAttribute('data-index', i);
                cell.setAttribute('data-device', deviceData.id);
                
                // 计算该单元格的时间范围
                const cellStartTime = new Date(startDate.getTime() + i * intervalMs);
                const cellEndTime = new Date(Math.min(cellStartTime.getTime() + intervalMs, endDate.getTime()));
                
                // 存储时间信息
                cell.setAttribute('data-start', cellStartTime.toISOString());
                cell.setAttribute('data-end', cellEndTime.toISOString());
                
                // 设置初始状态为"未知"
                cell.classList.add('status-unknown');
                
                // 为单元格添加悬停效果和点击事件
                cell.addEventListener('mouseenter', function() {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'tooltip';
                    tooltip.textContent = `${cellStartTime.toLocaleString()} - ${cellEndTime.toLocaleString()}`;
                    this.appendChild(tooltip);
                    
                    // 为单元格设置高亮效果
                    this.style.transform = 'scale(1.05)';
                    this.style.zIndex = '2';
                    this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                });
                
                cell.addEventListener('mouseleave', function() {
                    const tooltip = this.querySelector('.tooltip');
                    if (tooltip) {
                        this.removeChild(tooltip);
                    }
                    
                    // 移除高亮效果
                    this.style.transform = '';
                    this.style.zIndex = '';
                    this.style.boxShadow = '';
                });
                
                // 添加到文档片段
                fragment.appendChild(cell);
            }
            
            // 将文档片段添加到容器
            matrixContainer.appendChild(fragment);
            console.log(`矩阵单元格创建完成，总共${cellCount}个元素`);
            
            // 更新图例
            console.log('更新矩阵图例');
            
            // 定义updateMatrixLegend函数
            function updateMatrixLegend() {
                console.log('更新矩阵图例');
                
                // 获取设备类型
                const isPumpDevice = !['ph', 'water'].includes(deviceData.id);
                
                // 检查是否存在图例容器，如果不存在则创建
                let legendContainer = document.querySelector('.matrix-legend');
                if (!legendContainer) {
                    console.log('图例容器不存在，创建新容器');
                    legendContainer = document.createElement('div');
                    legendContainer.className = 'matrix-legend';
                    
                    // 找到矩阵容器并添加图例
                    const matrixContainer = document.getElementById('statusMatrix');
                    if (matrixContainer && matrixContainer.parentElement) {
                        matrixContainer.parentElement.appendChild(legendContainer);
                    } else {
                        // 如果找不到矩阵容器，添加到布尔历史部分
                        const booleanSection = document.getElementById('booleanHistorySection');
                        if (booleanSection) {
                            booleanSection.appendChild(legendContainer);
                        } else {
                            console.error('无法找到合适的容器添加图例');
                            return;
                        }
                    }
                }
                
                // 清空现有图例
                legendContainer.innerHTML = '';
                
                // 创建运行/停止图例
                const activeItem = document.createElement('div');
                activeItem.className = 'legend-item';
                
                const activeColor = document.createElement('div');
                activeColor.className = 'legend-color active';
                activeColor.style.backgroundColor = isPumpDevice ? '#4caf50' : '#2196f3';
                
                const activeText = document.createElement('div');
                activeText.className = 'legend-text';
                activeText.textContent = isPumpDevice ? '运行' : '正常范围';
                
                activeItem.appendChild(activeColor);
                activeItem.appendChild(activeText);
                
                const inactiveItem = document.createElement('div');
                inactiveItem.className = 'legend-item';
                
                const inactiveColor = document.createElement('div');
                inactiveColor.className = 'legend-color inactive';
                inactiveColor.style.backgroundColor = isPumpDevice ? '#f44336' : '#ff9800';
                
                const inactiveText = document.createElement('div');
                inactiveText.className = 'legend-text';
                inactiveText.textContent = isPumpDevice ? '停止' : '异常范围';
                
                inactiveItem.appendChild(inactiveColor);
                inactiveItem.appendChild(inactiveText);
                
                // 添加无数据图例
                const noDataItem = document.createElement('div');
                noDataItem.className = 'legend-item';
                
                const noDataColor = document.createElement('div');
                noDataColor.className = 'legend-color';
                noDataColor.style.backgroundColor = 'transparent';
                noDataColor.style.border = '1px solid #ddd';
                
                const noDataText = document.createElement('div');
                noDataText.className = 'legend-text';
                noDataText.textContent = '无数据';
                
                noDataItem.appendChild(noDataColor);
                noDataItem.appendChild(noDataText);
                
                // 添加到图例容器
                legendContainer.appendChild(activeItem);
                legendContainer.appendChild(inactiveItem);
                legendContainer.appendChild(noDataItem);
                
                console.log('图例更新完成');
            }
            
            updateMatrixLegend();
            
            // 根据实际数据更新状态矩阵
            updateDeviceHistoryStatus(deviceData.id, startDate, endDate);
        }
        
        // 根据实际数据更新状态矩阵
        function updateDeviceHistoryStatus(deviceId, startDate, endDate) {
            console.log(`获取设备历史数据: ${deviceId} ${startDate.getTime()} ${endDate.getTime()}`);
            
            // 获取OneNET历史数据
            const realDataPromise = fetchRealData(deviceId, startDate, endDate);
            
            realDataPromise.then(data => {
                console.log('获取到历史数据:', data);
                
                // 获取所有单元格
                const cells = document.querySelectorAll(`.matrix-cell[data-device="${deviceId}"]`);
                const cellCount = cells.length;
                
                if (!data || !data.data || !data.data.datastreams || data.data.datastreams.length === 0) {
                    console.log('获取到0个历史数据点');
                    // 没有数据点，所有单元格保持未知状态
                    return;
                }
                
                // 获取数据点
                const dataPoints = data.data.datastreams[0].datapoints || [];
                console.log(`获取到${dataPoints.length}个历史数据点`);
                
                // 计算每个单元格对应的时间段
                const diffMs = endDate.getTime() - startDate.getTime();
                const intervalMs = diffMs / cellCount;
                
                console.log(`每个时间段对应${cellCount / dataPoints.length}个单元格`);
                
                // 更新每个单元格的状态
                dataPoints.forEach(point => {
                    const timestamp = new Date(point.at).getTime();
                    const value = parseFloat(point.value);
                    
                    // 找到对应的单元格
                    cells.forEach(cell => {
                        const cellStartTime = new Date(cell.getAttribute('data-start')).getTime();
                        const cellEndTime = new Date(cell.getAttribute('data-end')).getTime();
                        
                        // 如果数据点在单元格的时间范围内
                        if (timestamp >= cellStartTime && timestamp <= cellEndTime) {
                            // 根据设备类型和数值确定状态
                            let status = 'unknown';
                            
                            if (deviceId === 'water') {
                                if (value < 30) {
                                    status = 'warning';
                                } else if (value > 80) {
                                    status = 'danger';
                                } else {
                                    status = 'normal';
                                }
                            } else if (deviceId === 'ph') {
                                if (value < 6.5) {
                                    status = 'warning';
                                } else if (value > 8.5) {
                                    status = 'danger';
                                } else {
                                    status = 'normal';
                                }
                            }
                            
                            // 更新单元格状态
                            cell.className = 'matrix-cell';
                            cell.classList.add(`status-${status}`);
                            cell.setAttribute('data-value', value);
                        }
                    });
                });
                
                console.log('矩阵已使用真实数据更新');
            })
            .catch(error => {
                console.error(' API返回错误:', error);
            });
        }
        
        // 获取设备历史数据
        function fetchRealData(deviceId, startDate, endDate) {
            console.log(`获取设备历史数据: ${deviceId} ${startDate.getTime()} ${endDate.getTime()}`);
            
            return new Promise((resolve, reject) => {
                // 创建token
                const params = {
                    author_key: '****************************************************************',
                    version: '2022-05-01',
                    user_id: '420568',
                };
                
                let token;
                try {
                    if (typeof createCommonTokenFunc !== 'function') {
                        throw new Error('createCommonToken函数未找到');
                    }
                    token = createCommonTokenFunc(params);
                } catch (error) {
                    reject(error);
                    return;
                }
                
                // 根据设备类型确定数据流ID
                let datastream_id = deviceId;
                
                // 使用window上的fetchHistoricalData函数
                const queryParams = {
                    productId: 'HTJ98Pjh4a',
                    deviceName: 'flotation',
                    datastreamId: datastream_id,
                    start: startDate.getTime(),
                    end: endDate.getTime(),
                    limit: 1000,
                    sort: '1',
                    accessKey: '****************************************************************'
                };
                
                if (typeof window.fetchHistoricalData === 'function') {
                    window.fetchHistoricalData(queryParams)
                        .then(response => resolve(response))
                        .catch(error => reject(error));
                } else {
                    // 如果函数不存在，则回退到原始的fetch方法
                    const url = new URL('https://iot-api.heclouds.com/thingmodel/query-device-property-history');
                    url.searchParams.append('product_id', 'HTJ98Pjh4a');
                    url.searchParams.append('device_name', 'flotation');
                    url.searchParams.append('identifier', datastream_id);
                    url.searchParams.append('start_time', String(startDate.getTime()));
                    url.searchParams.append('end_time', String(endDate.getTime()));
                    url.searchParams.append('limit', '1000');
                    url.searchParams.append('sort', '1');
                    
                    fetch(url, {
                        method: 'GET',
                        headers: {
                            'authorization': token
                        }
                    })
                    .then(response => response.json())
                    .then(data => resolve(data))
                    .catch(error => reject(error));
                }
            });
        }
        
        // 更新状态矩阵函数
        function updateStatusMatrix(chartData) {
            // 即使没有数据，也继续处理，显示为无数据状态
            if (!chartData || chartData.length === 0) {
                console.log('没有图表数据可用于更新状态矩阵，将显示为无数据状态');
                chartData = []; // 使用空数组继续处理
            } else {
                console.log(`更新状态矩阵，使用${chartData.length}个数据点`);
            }
            
            // 获取当前矩阵
            const matrix = document.getElementById('statusMatrix');
            if (!matrix) {
                console.error('找不到状态矩阵元素');
                return;
            }
            
            // 获取当前时间周期
            const periodOption = document.querySelector('.period-option.active');
            if (!periodOption) {
                console.error('找不到活动的时间周期选项');
                return;
            }
            
            const period = periodOption.getAttribute('data-period');
            const isPumpDevice = !['ph', 'water'].includes(deviceData.id);
            
            // 确定时间范围
            const now = new Date();
            let timeRange;
            
            switch(period) {
                case '3d': timeRange = 3 * 24 * 60 * 60 * 1000; break;
                case '5d': timeRange = 5 * 24 * 60 * 60 * 1000; break;
                case '7d': timeRange = 7 * 24 * 60 * 60 * 1000; break;
                default: timeRange = 24 * 60 * 60 * 1000; // 24h
            }
            
            const start = new Date(now.getTime() - timeRange);
            
            // 获取矩阵单元格数量
            const cells = matrix.querySelectorAll('.matrix-cell');
            if (cells.length === 0) {
                console.error('矩阵中没有单元格');
                return;
            }
            
            // 计算每个单元格代表的时间间隔
            const timePerCell = timeRange / cells.length;
            
            // 创建时间段数组，每个单元格对应一个时间段
            const timeSegments = Array(cells.length).fill().map((_, i) => {
                const segmentStart = new Date(start.getTime() + i * timePerCell);
                const segmentEnd = new Date(segmentStart.getTime() + timePerCell);
                return { start: segmentStart, end: segmentEnd, hasData: false, value: null };
            });
            
            // 将数据点分配到对应的时间段
            if (chartData && chartData.length > 0) {
                chartData.forEach(point => {
                    if (!point || !point.x) return; // 跳过无效数据点
                    
                    const pointTime = point.x;
                    const pointValue = point.y;
                    
                    // 找到对应的时间段
                    for (let i = 0; i < timeSegments.length; i++) {
                        const segment = timeSegments[i];
                        if (pointTime >= segment.start && pointTime < segment.end) {
                            segment.hasData = true;
                            
                            // 对于布尔型设备（泵），转换为布尔值
                            if (isPumpDevice) {
                                segment.value = pointValue === 1 || pointValue === '1' || pointValue === 'true' || pointValue === true;
                            } 
                            // 对于传感器，判断是否在正常范围内
                            else {
                                const numValue = parseFloat(pointValue);
                                if (deviceData.id === 'ph') {
                                    // PH值在6.5-7.5之间为正常
                                    segment.value = numValue >= 6.5 && numValue <= 7.5;
                                } else if (deviceData.id === 'water') {
                                    // 水位在70%-90%之间为正常
                                    segment.value = numValue >= 70 && numValue <= 90;
                                } else {
                                    segment.value = true; // 默认为正常
                                }
                            }
                            
                            break;
                        }
                    }
                });
            }
            
            // 更新矩阵单元格
            cells.forEach((cell, i) => {
                if (i < timeSegments.length) {
                    const segment = timeSegments[i];
                    
                    // 移除所有状态类
                    cell.classList.remove('active', 'inactive', 'no-data');
                    
                    if (!segment.hasData) {
                        cell.classList.add('no-data');
                        cell.style.backgroundColor = 'transparent';
                        cell.style.border = '1px solid #ddd';
                    } else if (segment.value === true) {
                        cell.classList.add('active');
                        cell.style.backgroundColor = isPumpDevice ? '#4caf50' : '#2196f3';
                        cell.style.color = 'white';
                    } else {
                        cell.classList.add('inactive');
                        cell.style.backgroundColor = isPumpDevice ? '#f44336' : '#ff9800';
                        cell.style.color = 'white';
                    }
                }
            });
            
            console.log('状态矩阵已更新');
        }
        
        // 更新波形统计信息
        function updateWaveformStatistics(values) {
            if (!values || values.length === 0) return;
            
            // 获取统计卡片元素
            const avgLevelElement = document.querySelector('.stat-card.up .stat-value');
            const changeRateElement = document.querySelector('.stat-card.down .stat-value');
            const alertCountElement = document.querySelector('.stat-card.neutral .stat-value');
            
            if (avgLevelElement) {
                // 计算平均水平
                const sum = values.reduce((a, b) => a + b, 0);
                const avg = sum / values.length;
                avgLevelElement.textContent = `${avg.toFixed(1)}%`;
                
                // 添加动画效果
                avgLevelElement.style.animation = 'none';
                setTimeout(() => {
                    avgLevelElement.style.animation = 'pulse 0.5s ease-in-out';
                }, 10);
            }
            
            if (changeRateElement) {
                // 计算变化率 (最后值与第一个值的差异百分比)
                if (values.length >= 2) {
                    const firstValue = values[0];
                    const lastValue = values[values.length - 1];
                    const changeRate = ((lastValue - firstValue) / firstValue) * 100;
                    const sign = changeRate >= 0 ? '+' : '';
                    changeRateElement.textContent = `${sign}${changeRate.toFixed(1)}%`;
                    
                    // 添加动画效果
                    changeRateElement.style.animation = 'none';
                    setTimeout(() => {
                        changeRateElement.style.animation = 'pulse 0.5s ease-in-out';
                    }, 10);
                }
            }
            
            if (alertCountElement) {
                // 计算异常值的数量 (超出平均值±20%的数据点)
                const sum = values.reduce((a, b) => a + b, 0);
                const avg = sum / values.length;
                const threshold = avg * 0.2; // 20%的阈值
                const alertCount = values.filter(v => Math.abs(v - avg) > threshold).length;
                alertCountElement.textContent = `${alertCount}次`;
                
                // 添加动画效果
                alertCountElement.style.animation = 'none';
                setTimeout(() => {
                    alertCountElement.style.animation = 'pulse 0.5s ease-in-out';
                }, 10);
            }
        }

        // 直接更新统计元素的函数
        function updateStatsDirectly(values) {
            if (!values || values.length === 0) {
                document.getElementById('avgValue').textContent = '0';
                document.getElementById('maxValue').textContent = '0';
                document.getElementById('minValue').textContent = '0';
                document.getElementById('stdValue').textContent = '0';
                return;
            }
            
            // 计算统计值
            const sum = values.reduce((a, b) => a + b, 0);
            const avg = sum / values.length;
            const max = Math.max(...values);
            const min = Math.min(...values);
            
            // 计算标准差
            const squareDiffs = values.map(value => {
                const diff = value - avg;
                return diff * diff;
            });
            const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;
            const std = Math.sqrt(avgSquareDiff);
            
            // 更新显示
            const avgElement = document.getElementById('avgValue');
            const maxElement = document.getElementById('maxValue');
            const minElement = document.getElementById('minValue');
            const stdElement = document.getElementById('stdValue');
            
            if (avgElement) avgElement.textContent = avg.toFixed(2);
            if (maxElement) maxElement.textContent = max.toFixed(2);
            if (minElement) minElement.textContent = min.toFixed(2);
            if (stdElement) stdElement.textContent = std.toFixed(2);
            
            // 添加动画效果以引起注意
            [avgElement, maxElement, minElement, stdElement].forEach(el => {
                if (el) {
                    el.style.animation = 'none';
                    setTimeout(() => {
                        el.style.animation = 'pulse 0.5s ease-in-out';
                    }, 10);
                }
            });
            
            console.log('已直接更新统计数据:', {avg: avg.toFixed(2), max: max.toFixed(2), min: min.toFixed(2), std: std.toFixed(2)});
        }

        // 确保updateDataStatistics函数在全局作用域中可用
        window.updateDataStatistics = function(values, hasData) {
            // 如果没有数据，所有统计值都显示为0
            if (!hasData || values.length === 0) {
                document.getElementById('avgValue').textContent = '0';
                document.getElementById('maxValue').textContent = '0';
                document.getElementById('minValue').textContent = '0';
                document.getElementById('stdValue').textContent = '0';
                return;
            }
            
            // 计算统计值
            const sum = values.reduce((a, b) => a + b, 0);
            const avg = sum / values.length;
            const max = Math.max(...values);
            const min = Math.min(...values);
            
            // 计算标准差
            const squareDiffs = values.map(value => {
                const diff = value - avg;
                return diff * diff;
            });
            const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;
            const std = Math.sqrt(avgSquareDiff);
            
            // 更新显示
            const avgElement = document.getElementById('avgValue');
            const maxElement = document.getElementById('maxValue');
            const minElement = document.getElementById('minValue');
            const stdElement = document.getElementById('stdValue');
            
            if (avgElement) avgElement.textContent = avg.toFixed(2);
            if (maxElement) maxElement.textContent = max.toFixed(2);
            if (minElement) minElement.textContent = min.toFixed(2);
            if (stdElement) stdElement.textContent = std.toFixed(2);
            
            // 添加动画效果以引起注意
            [avgElement, maxElement, minElement, stdElement].forEach(el => {
                if (el) {
                    el.style.animation = 'none';
                    setTimeout(() => {
                        el.style.animation = 'pulse 0.5s ease-in-out';
                    }, 10);
                }
            });
            
            console.log('已更新统计数据:', {avg: avg.toFixed(2), max: max.toFixed(2), min: min.toFixed(2), std: std.toFixed(2)});
            
            // 更新设备状态信息（仅当是传感器时）
            if (deviceData.id === 'ph' || deviceData.id === 'water') {
                if (typeof updateDeviceCurrentStatus === 'function') {
                    updateDeviceCurrentStatus(avg.toFixed(2));
                }
            }
            
            // 更新波形图统计信息
            if (typeof updateWaveformStatistics === 'function') {
                updateWaveformStatistics(values);
            }
        };
      </script>
</body>
</html> 