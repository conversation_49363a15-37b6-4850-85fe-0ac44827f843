<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能辐射监测APP图标生成器</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .container {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .icon-preview {
            display: inline-block;
            margin: 20px;
            position: relative;
        }
        
        .app-icon {
            width: 120px;
            height: 120px;
            border-radius: 26px;
            background: linear-gradient(135deg, #00b4d8 0%, #0096c7 50%, #0077b6 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 180, 216, 0.4);
            overflow: hidden;
        }
        
        .app-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            animation: rotate 10s linear infinite;
        }
        
        .icon-content {
            position: relative;
            z-index: 2;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .watch-outline {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255,255,255,0.9);
            border-radius: 50%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }
        
        .radiation-symbol {
            font-size: 24px;
            color: #FFD700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
            animation: pulse 2s ease-in-out infinite;
        }
        
        .signal-waves {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .wave {
            position: absolute;
            border: 2px solid rgba(255,255,255,0.6);
            border-radius: 50%;
            animation: wave-expand 2s ease-out infinite;
        }
        
        .wave:nth-child(1) {
            width: 80px;
            height: 80px;
            margin: -40px 0 0 -40px;
            animation-delay: 0s;
        }
        
        .wave:nth-child(2) {
            width: 100px;
            height: 100px;
            margin: -50px 0 0 -50px;
            animation-delay: 0.5s;
        }
        
        .wave:nth-child(3) {
            width: 120px;
            height: 120px;
            margin: -60px 0 0 -60px;
            animation-delay: 1s;
        }
        
        .tech-text {
            font-size: 8px;
            color: rgba(255,255,255,0.8);
            font-weight: bold;
            letter-spacing: 1px;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }
        
        @keyframes wave-expand {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }
        
        .download-btn {
            background: linear-gradient(135deg, #00b4d8, #0096c7);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0, 180, 216, 0.3);
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 180, 216, 0.4);
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        p {
            color: #666;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能辐射监测APP图标</h1>
        <p>专业的核辐射监测应用图标设计</p>
        
        <div class="icon-preview">
            <div class="app-icon" id="appIcon">
                <div class="signal-waves">
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                </div>
                <div class="icon-content">
                    <div class="watch-outline">
                        <div class="radiation-symbol">☢</div>
                    </div>
                    <div class="tech-text">MONITOR</div>
                </div>
            </div>
        </div>
        
        <br>
        <button class="download-btn" onclick="downloadIcon(512)">下载 512x512</button>
        <button class="download-btn" onclick="downloadIcon(256)">下载 256x256</button>
        <button class="download-btn" onclick="downloadIcon(128)">下载 128x128</button>
        <button class="download-btn" onclick="downloadIcon(64)">下载 64x64</button>
    </div>

    <script>
        function downloadIcon(size) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#00b4d8');
            gradient.addColorStop(0.5, '#0096c7');
            gradient.addColorStop(1, '#0077b6');
            
            // 绘制圆角矩形背景
            const radius = size * 0.22;
            ctx.fillStyle = gradient;
            roundRect(ctx, 0, 0, size, size, radius);
            ctx.fill();
            
            // 绘制光晕效果
            const glowGradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
            glowGradient.addColorStop(0, 'rgba(255,255,255,0.3)');
            glowGradient.addColorStop(0.7, 'rgba(255,255,255,0.1)');
            glowGradient.addColorStop(1, 'rgba(255,255,255,0)');
            ctx.fillStyle = glowGradient;
            ctx.fillRect(0, 0, size, size);
            
            // 绘制手表轮廓
            const watchRadius = size * 0.25;
            ctx.strokeStyle = 'rgba(255,255,255,0.9)';
            ctx.lineWidth = size * 0.025;
            ctx.beginPath();
            ctx.arc(size/2, size/2, watchRadius, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 绘制辐射符号
            ctx.fillStyle = '#FFD700';
            ctx.font = `${size * 0.2}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('☢', size/2, size/2 - size * 0.05);
            
            // 绘制文字
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.font = `bold ${size * 0.06}px Arial`;
            ctx.fillText('MONITOR', size/2, size/2 + size * 0.15);
            
            // 绘制信号波纹
            for (let i = 1; i <= 3; i++) {
                ctx.strokeStyle = `rgba(255,255,255,${0.3 - i * 0.08})`;
                ctx.lineWidth = size * 0.015;
                ctx.beginPath();
                ctx.arc(size/2, size/2, watchRadius + i * size * 0.08, 0, 2 * Math.PI);
                ctx.stroke();
            }
            
            // 下载图片
            const link = document.createElement('a');
            link.download = `radiation_monitor_icon_${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
    </script>
</body>
</html>
