# 智能家居系统后端数据同步服务说明

本文档描述了智能家居系统后端数据同步服务的实现方案，包括定时从云平台获取设备数据、保存到数据库，以及提供历史数据查询接口。

## 一、后端服务架构

后端服务主要包含以下几个模块：

1. **数据同步模块**：负责从云平台获取实时数据，或接收前端上报的设备数据
2. **数据存储模块**：负责将设备数据保存到数据库
3. **数据查询模块**：负责提供历史数据查询接口

## 二、数据同步实现方案

### 方案一：前端上报数据（已实现）

前端在获取云平台数据后，通过`/api/data/sync`接口将数据上报给后端服务，后端将数据保存到数据库。

```javascript
// 前端代码片段
syncDataToBackend() {
  uni.request({
    url: 'https://zarkychmvpiv.sealoshzh.site/api/data/sync',
    method: 'POST',
    header: {
      'Authorization': 'Bearer ' + uni.getStorageSync('token'),
      'Content-Type': 'application/json'
    },
    data: {
      ph: this.ph,
      water: this.Water,
      pump1: this.Pump1,
      pump2: this.Pump2,
      pump3: this.Pump3,
      pump4: this.Pump4,
      pump5: this.Pump5,
      pump6: this.Pump6,
      timestamp: new Date().toISOString()
    },
    success: (res) => {
      console.log('数据同步成功');
    },
    fail: (err) => {
      console.error('数据同步失败:', err);
    }
  });
}
```

### 方案二：后端定时任务（推荐实现）

后端服务定时从云平台获取设备数据，将数据保存到数据库。

#### 后端定时任务实现步骤：

1. **创建定时任务**：使用Node.js的定时任务库（如`node-cron`）或系统定时任务工具

```javascript
// 使用node-cron实现
const cron = require('node-cron');
const axios = require('axios');

// 每5秒执行一次
cron.schedule('*/5 * * * * *', async () => {
  try {
    // 生成访问令牌
    const token = createCommonToken({
      author_key: 'xaVmoFXwf9oB4QpVN8Vt8sL4hqhLoIyRp31g2j0gQKEt0VG5XEFbpYGvQst14YPX',
      version: '2022-05-01',
      user_id: '420568',
    });
    
    // 从云平台获取数据
    const response = await axios.get(
      'https://iot-api.heclouds.com/thingmodel/query-device-property',
      {
        params: {
          product_id: 'HTJ98Pjh4a',
          device_name: 'flotation',
        },
        headers: {
          Authorization: token
        }
      }
    );
    
    // 处理返回数据
    if (response.data && response.data.data) {
      const data = {
        ph: response.data.data[0].value,
        water: response.data.data[10].value,
        pump1: response.data.data[1].value === 'true',
        pump2: response.data.data[2].value === 'true',
        pump3: response.data.data[3].value === 'true',
        pump4: response.data.data[4].value === 'true',
        pump5: response.data.data[5].value === 'true',
        pump6: response.data.data[6].value === 'true',
        timestamp: new Date().toISOString()
      };
      
      // 保存到数据库
      await saveDataToDatabase(data);
      console.log('数据同步成功:', new Date());
    }
  } catch (error) {
    console.error('数据同步失败:', error);
  }
});
```

2. **实现云平台访问令牌生成函数**

```javascript
const crypto = require('crypto');

function createCommonToken(params) {
  const access_key = Buffer.from(params.author_key, "base64");

  const version = params.version;
  let res = 'userid' + '/' + params.user_id;
  const et = Math.ceil((Date.now() + 365 * 24 * 3600 * 1000) / 1000);
  const method = 'sha1';

  const key = et + "\n" + method + "\n" + res + "\n" + version;
  
  let sign = crypto.createHmac('sha1', access_key).update(key).digest().toString('base64');

  res = encodeURIComponent(res);
  sign = encodeURIComponent(sign);
  const token = `version=${version}&res=${res}&et=${et}&method=${method}&sign=${sign}`;

  return token;
}
```

3. **实现数据保存到数据库的函数**

```javascript
const mongoose = require('mongoose');

// 定义设备数据模型
const DeviceDataSchema = new mongoose.Schema({
  ph: String,
  water: String,
  pump1: Boolean,
  pump2: Boolean,
  pump3: Boolean,
  pump4: Boolean,
  pump5: Boolean,
  pump6: Boolean,
  timestamp: { type: Date, default: Date.now }
});

const DeviceData = mongoose.model('DeviceData', DeviceDataSchema);

// 保存数据到数据库
async function saveDataToDatabase(data) {
  try {
    const deviceData = new DeviceData(data);
    await deviceData.save();
    return true;
  } catch (error) {
    console.error('保存数据失败:', error);
    return false;
  }
}
```

## 三、数据查询API

### 1. 获取设备实时数据

- **URL**: `/api/data/realtime`
- **方法**: `GET`
- **请求参数**:
  ```
  deviceId: 设备ID
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "ph": "7.5",
      "water": "80",
      "pump1": true,
      "pump2": false,
      "pump3": true,
      "pump4": false,
      "pump5": true,
      "pump6": false,
      "timestamp": "2023-07-18T14:30:00Z"
    }
  }
  ```

### 2. 获取设备历史数据

- **URL**: `/api/data/history`
- **方法**: `GET`
- **请求参数**:
  ```
  type: 数据类型（ph/water/pump1/pump2/pump3/pump4/pump5/pump6）
  startTime: 开始时间（ISO格式）
  endTime: 结束时间（ISO格式）
  interval: 数据间隔（hour/day/week/month），可选
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "type": "ph",
      "unit": "",
      "dataPoints": [
        {
          "timestamp": "2023-07-18T14:00:00Z",
          "value": "7.5"
        },
        {
          "timestamp": "2023-07-18T15:00:00Z",
          "value": "7.6"
        }
      ],
      "statistics": {
        "max": "7.6",
        "min": "7.5",
        "avg": "7.55"
      }
    }
  }
  ```

### 3. 数据同步接口

- **URL**: `/api/data/sync`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "ph": "7.5",
    "water": "80",
    "pump1": true,
    "pump2": false,
    "pump3": true,
    "pump4": false,
    "pump5": true,
    "pump6": false,
    "timestamp": "2023-07-18T14:30:00Z"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "同步成功",
    "data": null
  }
  ```

## 四、后端实现示例代码

### 1. 数据模型定义

```javascript
// models/DeviceData.js
const mongoose = require('mongoose');

const DeviceDataSchema = new mongoose.Schema({
  ph: String,
  water: String,
  pump1: Boolean,
  pump2: Boolean,
  pump3: Boolean,
  pump4: Boolean,
  pump5: Boolean,
  pump6: Boolean,
  timestamp: { type: Date, default: Date.now }
});

module.exports = mongoose.model('DeviceData', DeviceDataSchema);
```

### 2. 历史数据查询接口实现

```javascript
// controllers/dataController.js
const DeviceData = require('../models/DeviceData');

// 获取历史数据
exports.getHistoryData = async (req, res) => {
  try {
    const { type, startTime, endTime, interval = 'hour' } = req.query;
    
    // 验证参数
    if (!type || !startTime || !endTime) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数',
        data: null
      });
    }
    
    // 构建查询条件
    const query = {
      timestamp: {
        $gte: new Date(startTime),
        $lte: new Date(endTime)
      }
    };
    
    // 查询数据
    const data = await DeviceData.find(query)
      .sort({ timestamp: 1 })
      .select(`${type} timestamp -_id`);
    
    // 转换数据格式
    const dataPoints = data.map(item => ({
      timestamp: item.timestamp.toISOString(),
      value: item[type]
    }));
    
    // 计算统计信息
    let statistics = {};
    if (dataPoints.length > 0) {
      const values = dataPoints.map(point => parseFloat(point.value) || 0);
      statistics = {
        max: Math.max(...values).toString(),
        min: Math.min(...values).toString(),
        avg: (values.reduce((sum, val) => sum + val, 0) / values.length).toFixed(2)
      };
    }
    
    res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        type,
        unit: '',
        dataPoints,
        statistics
      }
    });
  } catch (error) {
    console.error('获取历史数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      data: null
    });
  }
};

// 数据同步接口
exports.syncData = async (req, res) => {
  try {
    // 从请求体获取数据
    const data = req.body;
    
    // 创建新记录
    const deviceData = new DeviceData({
      ph: data.ph,
      water: data.water,
      pump1: data.pump1,
      pump2: data.pump2,
      pump3: data.pump3,
      pump4: data.pump4,
      pump5: data.pump5,
      pump6: data.pump6,
      timestamp: data.timestamp ? new Date(data.timestamp) : new Date()
    });
    
    // 保存数据
    await deviceData.save();
    
    res.status(200).json({
      code: 200,
      message: '同步成功',
      data: null
    });
  } catch (error) {
    console.error('数据同步失败:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      data: null
    });
  }
};
```

### 3. 路由定义

```javascript
// routes/dataRoutes.js
const express = require('express');
const router = express.Router();
const dataController = require('../controllers/dataController');
const authMiddleware = require('../middleware/authMiddleware');

// 应用认证中间件
router.use(authMiddleware);

// 获取历史数据
router.get('/history', dataController.getHistoryData);

// 数据同步接口
router.post('/sync', dataController.syncData);

module.exports = router;
```

## 五、部署说明

1. **安装依赖**:
   ```
   npm install express mongoose node-cron axios crypto
   ```

2. **配置数据库连接**:
   ```javascript
   // config/database.js
   const mongoose = require('mongoose');

   const connectDB = async () => {
     try {
       await mongoose.connect('mongodb://localhost:27017/smarthome', {
         useNewUrlParser: true,
         useUnifiedTopology: true,
       });
       console.log('数据库连接成功');
     } catch (error) {
       console.error('数据库连接失败:', error.message);
       process.exit(1);
     }
   };

   module.exports = connectDB;
   ```

3. **启动定时任务和服务**:
   ```javascript
   // app.js
   const express = require('express');
   const connectDB = require('./config/database');
   const dataRoutes = require('./routes/dataRoutes');
   require('./tasks/dataSync'); // 导入定时任务

   const app = express();

   // 连接数据库
   connectDB();

   // 中间件
   app.use(express.json());

   // 路由
   app.use('/api/data', dataRoutes);

   // 启动服务器
   const PORT = process.env.PORT || 5000;
   app.listen(PORT, () => console.log(`服务器运行在端口: ${PORT}`));
   ```

## 六、注意事项

1. **数据安全**：确保云平台的访问令牌安全存储，避免泄露
2. **异常处理**：完善错误处理机制，避免定时任务因异常而中断
3. **数据清理**：实现数据清理策略，避免数据量过大影响性能
4. **监控告警**：实现监控告警机制，及时发现并处理异常情况
5. **日志记录**：完善日志记录，方便问题排查和性能优化 