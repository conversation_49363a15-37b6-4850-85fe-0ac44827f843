<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="top-nav">
      <view class="back-button" @click="goBack">
        <text class="iconfont icon-back"></text>
      </view>
      <view class="page-title">个人中心</view>
      <view class="action-button" @click="showHelp">
        <text class="iconfont icon-help"></text>
      </view>
    </view>
    
    <!-- 用户资料部分 -->
    <view class="user-profile">
      <view class="user-avatar">
        <image :src="userInfo.avatar" mode="aspectFill"></image>
        <view class="edit-avatar" @click="editAvatar">编辑</view>
      </view>
      <view class="user-info">
        <view class="user-name">{{ userInfo.name }}</view>
        <view class="user-id">
          <text class="iconfont icon-user"></text>
          ID: {{ userInfo.id }}
        </view>
        <view class="edit-profile" @click="editProfile">
          <text class="iconfont icon-edit"></text>
          编辑资料
        </view>
      </view>
    </view>
    
    <!-- 设置内容区域 -->
    <scroll-view class="settings-content" scroll-y="true">
        <!-- 账户安全设置 -->
        <view class="settings-section">
          <view class="section-title">
            <text class="iconfont icon-lock"></text>
            账户与安全
          </view>
          
          <view class="settings-item" @click="showChangePasswordModal">
            <view class="settings-item-left">
              <view class="settings-icon">
                <text class="iconfont icon-lock"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">修改密码</view>
                <view class="settings-description">定期更新密码以保护账户安全</view>
              </view>
            </view>
            <view class="settings-item-right">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
          
          <view class="settings-item" @click="navigateToPrivacySettings">
            <view class="settings-item-left">
              <view class="settings-icon privacy-icon">
                <text class="iconfont icon-shield"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">隐私设置</view>
                <view class="settings-description">管理您的个人隐私选项</view>
              </view>
            </view>
            <view class="settings-item-right">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
        </view>
        
        <!-- 数据设置 -->
        <view class="settings-section">
          <view class="section-title">
            <text class="iconfont icon-data"></text>
            数据与通知
          </view>
          
          <view class="settings-item" @click="showDataFrequencyModal">
            <view class="settings-item-left">
              <view class="settings-icon data-icon">
                <text class="iconfont icon-time"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">数据更新频率</view>
                <view class="settings-description">选择您希望数据刷新的频率</view>
              </view>
            </view>
            <view class="settings-item-right">
              <view class="settings-value">{{ dataFrequency }}</view>
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
          
          <view class="settings-item">
            <view class="settings-item-left">
              <view class="settings-icon notification-icon">
                <text class="iconfont icon-notification"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">数据更新通知</view>
                <view class="settings-description">在数据更新时接收通知</view>
              </view>
            </view>
            <view class="settings-item-right">
              <switch 
                :checked="notificationEnabled" 
                @change="toggleNotification" 
                color="#2196f3"
                class="toggle-switch"
              />
            </view>
          </view>
          
          <view class="settings-item">
            <view class="settings-item-left">
              <view class="settings-icon sync-icon">
                <text class="iconfont icon-sync"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">后台数据同步</view>
                <view class="settings-description">允许应用在后台同步数据</view>
              </view>
            </view>
            <view class="settings-item-right">
              <switch 
                :checked="backgroundSyncEnabled" 
                @change="toggleBackgroundSync" 
                color="#4caf50"
                class="toggle-switch"
              />
            </view>
          </view>
        </view>
        
        <!-- 应用设置 -->
        <view class="settings-section">
          <view class="section-title">
            <text class="iconfont icon-setting"></text>
            应用与关于
          </view>
          
          <view class="settings-item" @click="navigateToLanguageSettings">
            <view class="settings-item-left">
              <view class="settings-icon language-icon">
                <text class="iconfont icon-language"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">语言设置</view>
                <view class="settings-description">更改应用显示语言</view>
              </view>
            </view>
            <view class="settings-item-right">
              <view class="settings-value">简体中文</view>
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
          
          <view class="settings-item">
            <view class="settings-item-left">
              <view class="settings-icon version-icon">
                <text class="iconfont icon-info"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">当前版本</view>
                <view class="settings-description">查看应用版本信息</view>
              </view>
            </view>
            <view class="settings-item-right">
              <view class="version-badge">v2.3.5</view>
            </view>
          </view>
          
          <view class="settings-item" @click="checkUpdate">
            <view class="settings-item-left">
              <view class="settings-icon update-icon">
                <text class="iconfont icon-update"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">检查更新</view>
                <view class="settings-description">检查应用最新版本</view>
              </view>
            </view>
            <view class="settings-item-right">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
        </view>
        
        <!-- 法律与帮助 -->
        <view class="settings-section">
          <view class="section-title">
            <text class="iconfont icon-help"></text>
            法律与帮助
          </view>
          
          <view class="settings-item" @click="navigateToUserAgreement">
            <view class="settings-item-left">
              <view class="settings-icon agreement-icon">
                <text class="iconfont icon-file"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">用户协议</view>
                <view class="settings-description">查看用户协议条款</view>
              </view>
            </view>
            <view class="settings-item-right">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
          
          <view class="settings-item" @click="navigateToPrivacyPolicy">
            <view class="settings-item-left">
              <view class="settings-icon privacy-policy-icon">
                <text class="iconfont icon-shield"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">隐私政策</view>
                <view class="settings-description">查看数据使用与隐私条款</view>
              </view>
            </view>
            <view class="settings-item-right">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
          
          <view class="settings-item" @click="contactSupport">
            <view class="settings-item-left">
              <view class="settings-icon support-icon">
                <text class="iconfont icon-message"></text>
              </view>
              <view class="settings-text">
                <view class="settings-label">联系客服</view>
                <view class="settings-description">获取问题解答与帮助</view>
              </view>
            </view>
            <view class="settings-item-right">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
        </view>
        
        <!-- 退出登录按钮 -->
        <button class="logout-button" @click="showLogoutConfirm">
          <text class="iconfont icon-logout"></text>
          退出登录
        </button>
      </scroll-view>
    
    <!-- 修改密码弹窗 -->
    <uni-popup ref="passwordPopup" type="center">
      <view class="modal-content">
        <view class="modal-title">修改密码</view>
        <input 
          type="password" 
          class="modal-input" 
          placeholder="当前密码" 
          v-model="passwordForm.currentPassword"
        />
        <input 
          type="password" 
          class="modal-input" 
          placeholder="新密码" 
          v-model="passwordForm.newPassword"
        />
        <input 
          type="password" 
          class="modal-input" 
          placeholder="确认新密码" 
          v-model="passwordForm.confirmPassword"
        />
        <view class="modal-buttons">
          <view class="modal-button cancel-button" @click="closePasswordModal">取消</view>
          <view class="modal-button confirm-button" @click="confirmChangePassword">确认</view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 数据频率选择弹窗 -->
    <uni-popup ref="frequencyPopup" type="center">
      <view class="modal-content">
        <view class="modal-title">选择数据更新频率</view>
        <view class="frequency-options">
          <radio-group @change="onFrequencyChange">
            <label class="frequency-option" v-for="(option, index) in frequencyOptions" :key="index">
              <radio 
                :value="option.value" 
                :checked="dataFrequency === option.value" 
                color="#2196f3"
              />
              <text>{{ option.label }}</text>
            </label>
          </radio-group>
        </view>
        <view class="modal-buttons">
          <view class="modal-button cancel-button" @click="closeFrequencyModal">取消</view>
          <view class="modal-button confirm-button" @click="confirmFrequency">确认</view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 退出确认弹窗 -->
    <uni-popup ref="logoutPopup" type="center">
      <view class="modal-content">
        <view class="modal-title">确认退出登录</view>
        <view class="modal-message">您确定要退出登录吗？</view>
        <view class="modal-buttons">
          <view class="modal-button cancel-button" @click="closeLogoutModal">取消</view>
          <view class="modal-button confirm-button" @click="confirmLogout">确认</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue';

export default {
  components: {
    uniPopup
  },
  data() {
    return {
      userInfo: {
        name: '张工程师',
        id: '1001234',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
      },
      dataFrequency: '每5分钟',
      notificationEnabled: true,
      backgroundSyncEnabled: true,
      selectedFrequency: '每5分钟',
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      frequencyOptions: [
        { label: '每1分钟', value: '每1分钟' },
        { label: '每5分钟', value: '每5分钟' },
        { label: '每10分钟', value: '每10分钟' },
        { label: '每30分钟', value: '每30分钟' },
        { label: '每1小时', value: '每1小时' }
      ]
    };
  },
  methods: {
    goBack() {
      if (getCurrentPages().length > 1) {
        uni.navigateBack();
      } else {
        uni.switchTab({
          url: '/pages/home/<USER>'
        });
      }
    },
    showHelp() {
      uni.showToast({
        title: '如需帮助，请联系客服或查看用户手册',
        icon: 'none'
      });
    },
    editAvatar() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.userInfo.avatar = res.tempFilePaths[0];
          uni.showToast({
            title: '头像已更新',
            icon: 'success'
          });
        }
      });
    },
    editProfile() {
      uni.showToast({
        title: '编辑资料功能开发中',
        icon: 'none'
      });
    },
    showChangePasswordModal() {
      this.$refs.passwordPopup.open();
    },
    closePasswordModal() {
      this.$refs.passwordPopup.close();
      this.clearPasswordForm();
    },
    confirmChangePassword() {
      const { currentPassword, newPassword, confirmPassword } = this.passwordForm;
      
      if (!currentPassword || !newPassword || !confirmPassword) {
        uni.showToast({
          title: '请填写所有密码字段',
          icon: 'none'
        });
        return;
      }
      
      if (newPassword !== confirmPassword) {
        uni.showToast({
          title: '新密码与确认密码不一致',
          icon: 'none'
        });
        return;
      }
      
      // 这里添加修改密码的逻辑
      uni.showToast({
        title: '密码修改成功',
        icon: 'success'
      });
      
      this.closePasswordModal();
    },
    clearPasswordForm() {
      this.passwordForm = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
    },
    showDataFrequencyModal() {
      this.selectedFrequency = this.dataFrequency;
      this.$refs.frequencyPopup.open();
    },
    closeFrequencyModal() {
      this.$refs.frequencyPopup.close();
    },
    onFrequencyChange(e) {
      this.selectedFrequency = e.detail.value;
    },
    confirmFrequency() {
      this.dataFrequency = this.selectedFrequency;
      this.closeFrequencyModal();
    },
    toggleNotification(e) {
      this.notificationEnabled = e.detail.value;
    },
    toggleBackgroundSync(e) {
      this.backgroundSyncEnabled = e.detail.value;
    },
    navigateToLanguageSettings() {
      uni.showToast({
        title: '语言设置功能开发中',
        icon: 'none'
      });
    },
    checkUpdate() {
      uni.showLoading({
        title: '正在检查更新'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '当前已是最新版本',
          icon: 'success'
        });
      }, 1000);
    },
    navigateToUserAgreement() {
      uni.navigateTo({
        url: '/pages/agreement/agreement'
      });
    },
    navigateToPrivacyPolicy() {
      uni.navigateTo({
        url: '/pages/privacy/privacy'
      });
    },
    navigateToPrivacySettings() {
      uni.navigateTo({
        url: '/pages/privacy-settings/privacy-settings'
      });
    },
    contactSupport() {
      uni.showActionSheet({
        itemList: ['在线客服', '电话咨询', '邮件反馈'],
        success: (res) => {
          uni.showToast({
            title: '客服功能开发中',
            icon: 'none'
          });
        }
      });
    },
    showLogoutConfirm() {
      this.$refs.logoutPopup.open();
    },
    closeLogoutModal() {
      this.$refs.logoutPopup.close();
    },
    confirmLogout() {
      // 清除本地存储的登录信息
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
      
      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/index/index'
      });
    }
  }
};
</script>

<style>
.container {
  display: flex;
  width: 100%;
  height: 100vh;
  flex-direction: column;
  background: linear-gradient(120deg, #e0f7fa 0%, #f5f5f5 100%);
}

/* 顶部导航栏 */
.top-nav {
  display: flex;
  justify-content: space-between;
  padding: 16px 20px;
  align-items: center;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.back-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.action-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

/* 用户资料部分 */
.user-profile {
  display: flex;
  align-items: center;
  padding: 20px;
  margin: 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  flex-shrink: 0;
}

.user-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.edit-avatar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 12px;
  text-align: center;
  padding: 4px 0;
}

.user-info {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-id {
  font-size: 14px;
  color: #999;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.user-id .iconfont {
  margin-right: 5px;
}

.edit-profile {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  align-self: flex-start;
}

.edit-profile .iconfont {
  margin-right: 5px;
}

/* 设置内容区域 */
.settings-content {
  flex: 1;
  padding: 0 15px 20px;
  overflow-y: auto;
  margin-bottom: 60px;
}

.settings-content::-webkit-scrollbar {
  display: none;
}

.settings-section {
  background-color: white;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.section-title .iconfont {
  margin-right: 8px;
  color: #666;
}

.settings-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.settings-item:last-child {
  border-bottom: none;
  padding-bottom: 5px;
}

.settings-item:first-child {
  padding-top: 5px;
}

.settings-item-left {
  display: flex;
  flex: 1;
  align-items: center;
  min-width: 0; /* 防止内容溢出 */
}

.settings-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.settings-icon .iconfont {
  font-size: 20px;
  color: #666;
}

.settings-text {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

.settings-label {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.settings-description {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.settings-item-right {
  display: flex;
  align-items: center;
  margin-left: 15px;
  flex-shrink: 0;
}

.settings-value {
  font-size: 14px;
  color: #999;
  margin-right: 8px;
}

.settings-item-right .iconfont {
  font-size: 18px;
  color: #ccc;
}

.version-badge {
  background-color: #f5f5f5;
  color: #666;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 10px;
}

/* 图标颜色 */
.privacy-icon .iconfont {
  color: #9c27b0;
}

.data-icon .iconfont {
  color: #2196f3;
}

.notification-icon .iconfont {
  color: #ff9800;
}

.sync-icon .iconfont {
  color: #4caf50;
}

.language-icon .iconfont {
  color: #3f51b5;
}

.version-icon .iconfont {
  color: #607d8b;
}

.update-icon .iconfont {
  color: #00bcd4;
}

.agreement-icon .iconfont {
  color: #795548;
}

.privacy-policy-icon .iconfont {
  color: #9c27b0;
}

.support-icon .iconfont {
  color: #009688;
}

/* 开关按钮 */
.toggle-switch {
  transform: scale(0.8);
}

/* 退出按钮 */
.logout-button {
  margin: 20px 0 40px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 10px;
  height: 50px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-button .iconfont {
  margin-right: 8px;
}
</style>