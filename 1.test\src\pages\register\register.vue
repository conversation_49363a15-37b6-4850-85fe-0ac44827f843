<template>
    <view class="container">
      <!-- 原型部分 -->
      <view class="prototype">
        <view class="register-container">
          <view class="content-wrapper">
            <view class="logo-container">
              <view class="logo">SmartHome</view>
            </view>
            
            <text class="title">创建账号</text>
            <text class="subtitle">加入智能家居系统，享受智能生活</text>
            
            <view class="progress-bar">
              <view class="progress-fill"></view>
            </view>
            
            <view class="form-section">
              <view class="form-section-title">基本信息</view>
              
              <view class="input-row">
                <view class="input-container">
                  <view class="input-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                  </view>
                  <input type="text" id="firstName" class="input-field input-with-icon" placeholder="姓氏" v-model="firstName">
                </view>
                
                <view class="input-container">
                  <input type="text" id="lastName" class="input-field" placeholder="名字" v-model="lastName">
                </view>
              </view>
              
              <view class="input-container">
                <view class="input-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                </view>
                <input type="email" id="email" class="input-field input-with-icon" placeholder="电子邮箱" v-model="email" @input="validateEmail">
                <view class="validation-message" :class="emailValidationClass" v-if="emailValidationMessage">{{emailValidationMessage}}</view>
              </view>
            </view>
            
            <view class="form-section">
              <view class="form-section-title">验证码确认</view>
              
              <view class="verification-container">
                <view class="input-container">
                  <view class="input-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                      <path d="M2 17l10 5 10-5"></path>
                      <path d="M2 12l10 5 10-5"></path>
                    </svg>
                  </view>
                  <input type="text" id="verificationCode" class="input-field input-with-icon" placeholder="请输入右侧验证码" v-model="verificationCode" @input="validateCode">
                  <view class="validation-message" :class="codeValidationClass" v-if="codeValidationMessage">{{codeValidationMessage}}</view>
                </view>
                <view class="captcha-container" id="captchaDisplay">
                  {{currentCaptcha}}
                  <button class="refresh-captcha" id="refreshCaptcha" @click="refreshCaptcha">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M23 4v6h-6"></path>
                      <path d="M1 20v-6h6"></path>
                      <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                      <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                    </svg>
                  </button>
                </view>
              </view>
            </view>
            
            <view class="form-section">
              <view class="form-section-title">设置密码</view>
              
              <view class="input-container">
                <view class="input-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                </view>
                <input type="password" id="password" class="input-field input-with-icon" placeholder="密码（至少8位，包含字母和数字）" v-model="password" @input="validatePassword">
                <view class="validation-message" :class="passwordValidationClass" v-if="passwordValidationMessage">{{passwordValidationMessage}}</view>
              </view>
              
              <view class="input-container">
                <view class="input-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                </view>
                <input type="password" id="confirmPassword" class="input-field input-with-icon" placeholder="确认密码" v-model="confirmPassword" @input="validateConfirmPassword">
                <view class="validation-message" :class="confirmPasswordValidationClass" v-if="confirmPasswordValidationMessage">{{confirmPasswordValidationMessage}}</view>
              </view>
            </view>
            
            <view class="checkbox-container">
              <input type="checkbox" id="termsCheckbox" class="checkbox-input" v-model="termsChecked">
              <label for="termsCheckbox" class="checkbox-label">我已阅读并同意 <text class="link" @click="navigateToAgreement('terms')">用户协议</text> 和 <text class="link" @click="navigateToAgreement('privacy')">隐私政策</text></label>
            </view>
            
            <button class="register-button" id="registerButton" @click="register">注 册</button>
            
            <view class="login-prompt">
              已有账号？<text class="link" @click="navigateToLogin">立即登录</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 提示弹窗容器 -->
      <view class="toast-container" v-if="toast.show">
        <view class="toast" :class="toast.type" @click="hideToast">
          <view class="toast-icon">
            <svg v-if="toast.type === 'success'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <svg v-else-if="toast.type === 'error'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="15" y1="9" x2="9" y2="15"></line>
              <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="16" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
          </view>
          <view class="toast-content">
            <text class="toast-title">{{toast.title}}</text>
            <text class="toast-message">{{toast.message}}</text>
          </view>
          <view class="toast-progress">
            <view class="toast-progress-bar" :style="{width: toastProgress + '%'}"></view>
          </view>
        </view>
      </view>
    </view>
  </template>
  
  <script>
  export default {
    data() {
      return {
        firstName: '',
        lastName: '',
        email: '',
        verificationCode: '',
        password: '',
        confirmPassword: '',
        termsChecked: false,
        
        emailValidationClass: '',
        emailValidationMessage: '',
        codeValidationClass: '',
        codeValidationMessage: '',
        passwordValidationClass: '',
        passwordValidationMessage: '',
        confirmPasswordValidationClass: '',
        confirmPasswordValidationMessage: '',
        
        currentCaptcha: 'A7B3X5',
        
        toast: {
          show: false,
          type: 'info',
          title: '',
          message: '',
        },
        toastProgress: 100,
        toastTimer: null
      };
    },
    onLoad() {
      // 隐藏导航栏
      uni.hideNavigationBarTitle();
    },
    mounted() {
      this.generateCaptcha();
      this.showToast('info', '欢迎', '请填写表单完成注册');
      
      // 从URL参数检查是否已同意条款
      if (uni.getStorageSync('agreed') === 'true') {
        this.termsChecked = true;
      }
    },
    methods: {
      generateCaptcha() {
        const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let captcha = '';
        for (let i = 0; i < 6; i++) {
          captcha += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        this.currentCaptcha = captcha;
      },
      refreshCaptcha() {
        this.generateCaptcha();
      },
      validateEmail() {
        if (!this.email) {
          this.emailValidationClass = '';
          this.emailValidationMessage = '';
          return;
        }
        
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        const isValid = re.test(this.email.toLowerCase());
        
        this.emailValidationClass = isValid ? 'success' : 'error';
        this.emailValidationMessage = isValid ? '邮箱格式正确' : '请输入有效的电子邮箱地址';
      },
      validateCode() {
        if (!this.verificationCode) {
          this.codeValidationClass = '';
          this.codeValidationMessage = '';
          return;
        }
        
        const isValid = this.verificationCode.toUpperCase() === this.currentCaptcha;
        
        this.codeValidationClass = isValid ? 'success' : 'error';
        this.codeValidationMessage = isValid ? '验证码正确' : '验证码不匹配';
      },
      validatePassword() {
        if (!this.password) {
          this.passwordValidationClass = '';
          this.passwordValidationMessage = '';
          return;
        }
        
        const re = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/;
        const isValid = re.test(this.password);
        
        this.passwordValidationClass = isValid ? 'success' : 'error';
        this.passwordValidationMessage = isValid ? '密码格式正确' : '密码至少需要8位，包含字母和数字';
        
        // 如果确认密码不为空，则同时验证两个密码是否一致
        if (this.confirmPassword) {
          this.validateConfirmPassword();
        }
      },
      validateConfirmPassword() {
        if (!this.confirmPassword) {
          this.confirmPasswordValidationClass = '';
          this.confirmPasswordValidationMessage = '';
          return;
        }
        
        const isValid = this.password === this.confirmPassword;
        
        this.confirmPasswordValidationClass = isValid ? 'success' : 'error';
        this.confirmPasswordValidationMessage = isValid ? '两次密码输入一致' : '两次输入的密码不一致';
      },
      showToast(type, title, message, duration = 3000) {
        // 清除之前的toast定时器
        if (this.toastTimer) {
          clearInterval(this.toastTimer);
        }
        
        this.toast = {
          show: true,
          type,
          title,
          message
        };
        
        this.toastProgress = 100;
        
        // 设置进度条动画
        const interval = 50;
        const steps = duration / interval;
        const stepValue = 100 / steps;
        
        this.toastTimer = setInterval(() => {
          this.toastProgress -= stepValue;
          
          if (this.toastProgress <= 0) {
            clearInterval(this.toastTimer);
            this.toast.show = false;
          }
        }, interval);
      },
      hideToast() {
        this.toast.show = false;
        if (this.toastTimer) {
          clearInterval(this.toastTimer);
        }
      },
      register() {
        // 验证所有字段
        let hasError = false;
        
        // 检查必填字段
        if (!this.firstName) {
          hasError = true;
          this.showToast('error', '输入错误', '请输入姓氏');
        } else if (!this.lastName) {
          hasError = true;
          this.showToast('error', '输入错误', '请输入名字');
        } else if (!this.email) {
          hasError = true;
          this.showToast('error', '输入错误', '请输入电子邮箱');
        } else if (!this.emailValidationClass || this.emailValidationClass !== 'success') {
          hasError = true;
          this.showToast('error', '格式错误', '请输入有效的电子邮箱地址');
        } else if (!this.verificationCode) {
          hasError = true;
          this.showToast('error', '输入错误', '请输入验证码');
        } else if (!this.codeValidationClass || this.codeValidationClass !== 'success') {
          hasError = true;
          this.showToast('error', '验证失败', '验证码输入错误，请重新输入');
        } else if (!this.password) {
          hasError = true;
          this.showToast('error', '输入错误', '请设置密码');
        } else if (!this.passwordValidationClass || this.passwordValidationClass !== 'success') {
          hasError = true;
          this.showToast('error', '密码不符合要求', '密码至少需要8位，包含字母和数字');
        } else if (!this.confirmPassword) {
          hasError = true;
          this.showToast('error', '输入错误', '请确认密码');
        } else if (!this.confirmPasswordValidationClass || this.confirmPasswordValidationClass !== 'success') {
          hasError = true;
          this.showToast('error', '密码不匹配', '两次输入的密码不一致');
        } else if (!this.termsChecked) {
          hasError = true;
          this.showToast('error', '条款确认', '请阅读并同意用户协议和隐私政策');
        }
        
        if (hasError) {
          return;
        }
        
        // 验证通过，模拟注册成功
        this.showToast('success', '注册成功', '您的账号已创建，即将跳转到登录页面');
        
        // 延迟跳转到登录页面
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/index/index'
          });
        }, 2000);
      },
      navigateToAgreement(type) {
        uni.navigateTo({
          url: `/pages/agreement/agreement?source=${type}`
        });
      },
      navigateToLogin() {
        uni.redirectTo({
          url: '/pages/index/index'
        });
      }
    }
  };
  </script>
  
  <style>
  /* 保留原有的所有样式，仅做少量适配调整 */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  }
  
  body {
    background: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    height: 100vh;
    overflow: auto;
  }
  
  .container {
    display: flex;
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    background: none;
  }
  
  /* 原型部分 */
  .prototype {
    width: 100%;
    max-width: 375px;
    height: auto;
    aspect-ratio: 375/812;
    background: linear-gradient(145deg, #f0f0f0, #fafafa);
    border-radius: 50px;
    overflow: auto;
    box-shadow: 0 20px 60px -10px rgba(30, 136, 229, 0.18);
    position: relative;
    max-height: 92vh;
  }
  
  .register-container {
    padding: 7% 5% 5%;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    overflow: auto;
    box-shadow: inset 0 0 24px rgba(30, 136, 229, 0.06);
    background: rgba(255,255,255,0.98);
    backdrop-filter: blur(2px);
  }
  
  .register-container::-webkit-scrollbar {
    width: 4px;
  }
  
  .register-container::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .register-container::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 20px;
  }
  
  .register-container::before {
    content: "";
    position: absolute;
    top: -150px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(30, 136, 229, 0.1), rgba(30, 136, 229, 0.05));
    z-index: 0;
  }
  
  .register-container::after {
    content: "";
    position: absolute;
    bottom: -150px;
    left: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(30, 136, 229, 0.05), rgba(30, 136, 229, 0.02));
    z-index: 0;
  }
  
  .content-wrapper {
    position: relative;
    z-index: 1;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .logo-container {
    margin-bottom: 20px;
    text-align: center;
  }
  
  .logo {
    font-size: 32px;
    font-weight: 700;
    color: #222;
    position: relative;
    display: inline-block;
  }
  
  .logo::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #1e88e5, #64b5f6);
    border-radius: 2px;
  }
  
  .title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 4px;
    color: #222;
    letter-spacing: -0.5px;
    display: block;
  }
  
  .subtitle {
    font-size: 16px;
    color: #999;
    margin-bottom: 30px;
    display: block;
  }
  
  .form-section {
    margin-bottom: 16px;
  }
  
  .form-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
  }
  
  .form-section-title::after {
    content: "";
    flex: 1;
    height: 1px;
    background-color: #e0e0e0;
    margin-left: 10px;
  }
  
  .input-container {
    position: relative;
    width: 100%;
    margin-bottom: 16px;
  }
  
  .input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
    pointer-events: none;
  }
  
  .input-icon svg {
    width: 20px;
    height: 20px;
  }
  
  .input-field {
    width: 100%;
    padding: 16px 20px;
    font-size: 16px;
    border: none;
    border-radius: 16px;
    background-color: #fff;
    color: #333;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
  
  .input-with-icon {
    padding-left: 50px;
  }
  
  .input-field:focus {
    outline: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(30, 136, 229, 0.2);
    transform: translateY(-2px);
  }
  
  /* 错误状态 */
  .input-field.error {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 0 0 2px rgba(244, 67, 54, 0.2);
  }
  
  /* 成功状态 */
  .input-field.success {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
  
  /* 验证消息 */
  .validation-message {
    font-size: 12px;
    margin-top: 6px;
    padding-left: 10px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
  }
  
  .validation-message.error {
    display: flex;
    color: #f44336;
  }
  
  .validation-message.success {
    display: flex;
    color: #4caf50;
  }
  
  .validation-message svg {
    width: 14px;
    height: 14px;
    margin-right: 6px;
  }
  
  .input-row {
    display: flex;
    gap: 12px;
  }
  
  .input-row .input-container {
    flex: 1;
  }
  
  .verification-container {
    display: flex;
    gap: 12px;
    align-items: center;
  }
  
  .verification-container .input-container {
    flex: 1;
  }
  
  .captcha-container {
    padding: 10px 16px;
    background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
    color: #333;
    font-size: 22px;
    font-weight: 700;
    letter-spacing: 4px;
    text-align: center;
    border-radius: 16px;
    min-width: 120px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    user-select: none;
    font-family: 'Courier New', monospace;
  }
  
  .captcha-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(45deg, transparent 45%, rgba(0,0,0,0.05) 50%, transparent 55%),
        linear-gradient(-45deg, transparent 45%, rgba(0,0,0,0.05) 50%, transparent 55%);
    background-size: 6px 6px;
    z-index: 1;
  }
  
  .refresh-captcha {
    position: absolute;
    right: 6px;
    bottom: 6px;
    width: 16px;
    height: 16px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.6;
    transition: all 0.3s ease;
  }
  
  .refresh-captcha:hover {
    opacity: 1;
    transform: rotate(180deg);
  }
  
  .refresh-captcha svg {
    width: 100%;
    height: 100%;
  }
  
  .verification-button {
    padding: 0 16px;
    background: linear-gradient(135deg, #64b5f6, #1e88e5);
    color: white;
    border: none;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    white-space: nowrap;
  }
  
  .verification-button:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .verification-button:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    margin-top: 8px;
  }
  
  /* 隐藏原生复选框但保持其可访问性 */
  .checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }
  
  /* 自定义复选框外观 */
  .checkbox-label {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  
  .checkbox-label:before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 6px;
    border: 2px solid #ccc;
    margin-right: 10px;
    background-color: white;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }
  
  /* 选中状态 */
  .checkbox-input:checked + .checkbox-label:before {
    background-color: #1e88e5;
    border-color: #1e88e5;
  }
  
  /* 勾选标记 */
  .checkbox-input:checked + .checkbox-label:after {
    content: '';
    position: absolute;
    left: 7px;
    top: 9px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }
  
  .link {
    color: #1e88e5;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
  }
  
  .link:hover {
    text-decoration: underline;
  }
  
  .register-button {
    background: linear-gradient(135deg, #222, #444);
    color: white;
    border: none;
    border-radius: 16px;
    padding: 18px;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    margin-bottom: 16px;
  }
  
  .register-button::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
  }
  
  .register-button:hover::after {
    left: 100%;
  }
  
  .register-button:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  /* 提示弹窗 */
  .toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    width: 320px;
    max-width: 90%;
    pointer-events: none;
  }
  
  .toast {
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 16px;
    background-color: white;
    color: #333;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
    overflow: hidden;
    animation: toastIn 0.3s forwards;
  }
  
  @keyframes toastIn {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  .toast.success {
    border-left: 4px solid #4caf50;
  }
  
  .toast.error {
    border-left: 4px solid #f44336;
  }
  
  .toast.info {
    border-left: 4px solid #2196f3;
  }
  
  .toast-icon {
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .toast-icon svg {
    width: 20px;
    height: 20px;
  }
  
  .toast.success .toast-icon {
    color: #4caf50;
  }
  
  .toast.error .toast-icon {
    color: #f44336;
  }
  
  .toast.info .toast-icon {
    color: #2196f3;
  }
  
  .toast-content {
    flex: 1;
  }
  
  .toast-title {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 16px;
    display: block;
  }
  
  .toast-message {
    font-size: 14px;
    color: #666;
    display: block;
  }
  
  .toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(0, 0, 0, 0.1);
    width: 100%;
  }
  
  .toast-progress-bar {
    height: 100%;
    transition: width linear;
  }
  
  .toast.success .toast-progress-bar {
    background-color: #4caf50;
  }
  
  .toast.error .toast-progress-bar {
    background-color: #f44336;
  }
  
  .toast.info .toast-progress-bar {
    background-color: #2196f3;
  }
  
  /* 登录提示 */
  .login-prompt {
    text-align: center;
    font-size: 14px;
    color: #999;
    margin: 16px 0;
  }
  
  .login-prompt .link {
    color: #1e88e5;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  
  .login-prompt .link:hover {
    text-decoration: underline;
  }
  
  /* 进度条 */
  .progress-bar {
    width: 100%;
    height: 4px;
    background-color: #e0e0e0;
    border-radius: 2px;
    margin-bottom: 24px;
    overflow: hidden;
  }
  
  .progress-fill {
    height: 100%;
    width: 50%;
    background: linear-gradient(90deg, #1e88e5, #64b5f6);
    border-radius: 2px;
    transition: width 0.3s ease;
  }
  
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  @media (max-width: 1000px) {
    .container {
      flex-direction: column;
      align-items: center;
      padding: 0;
    }
    .prototype {
      max-width: 100vw;
      max-height: 90vh;
      border-radius: 30px;
    }
    .register-container {
      padding: 10% 5%;
    }
  }
  @media (max-width: 480px) {
    .prototype {
      border-radius: 18px;
      max-height: 98vh;
    }
  }
  </style>