# 智能家居系统部署说明

## 一、系统架构

本智能家居系统采用前后端分离架构：

1. **前端**：使用uni-app框架开发的跨平台应用，可编译为H5、微信小程序等多平台应用
2. **后端**：基于Node.js + Express + MongoDB开发的RESTful API服务
3. **数据源**：OneNET云平台提供的物联网设备数据

## 二、前端部署

### 1. 环境准备

- Node.js >= 14.0.0
- npm >= 6.0.0
- HBuilderX (推荐使用)

### 2. 安装依赖

```bash
# 进入项目目录
cd smart-home-frontend

# 安装依赖
npm install
```

### 3. 开发环境运行

```bash
# HBuilderX中运行
# 或使用命令行运行H5版本
npm run dev:h5
```

### 4. 生产环境打包

```bash
# 打包H5版本
npm run build:h5

# 打包微信小程序
npm run build:mp-weixin
```

### 5. 部署H5版本

将`dist/build/h5`目录下的文件部署到Web服务器（如Nginx）：

```nginx
# Nginx配置示例
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/dist/build/h5;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理配置
    location /api/ {
        proxy_pass https://zarkychmvpiv.sealoshzh.site/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 三、后端部署

### 1. 环境准备

- Node.js >= 14.0.0
- MongoDB >= 4.0.0
- PM2 (推荐用于生产环境进程管理)

### 2. 安装依赖

```bash
# 进入后端项目目录
cd smart-home-backend

# 安装依赖
npm install

# 安装PM2
npm install -g pm2
```

### 3. 配置环境变量

创建`.env`文件：

```
PORT=5000
MONGO_URI=mongodb://localhost:27017/smarthome
JWT_SECRET=your_jwt_secret_key
NODE_ENV=production
```

### 4. 数据库初始化

确保MongoDB服务已启动，数据库将在应用首次运行时自动创建。

### 5. 启动服务

开发环境：

```bash
npm run dev
```

生产环境：

```bash
# 使用PM2启动
pm2 start app.js --name "smart-home-api"

# 设置开机自启
pm2 startup
pm2 save
```

### 6. 配置反向代理

使用Nginx配置反向代理：

```nginx
server {
    listen 80;
    server_name api.your-domain.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 四、数据同步服务部署

### 1. 安装依赖

```bash
npm install node-cron axios crypto mongoose
```

### 2. 配置定时任务

将定时任务脚本部署到服务器，并使用PM2管理：

```bash
# 启动定时任务
pm2 start dataSync.js --name "data-sync-service"

# 设置开机自启
pm2 save
```

### 3. 配置日志

```javascript
// 配置日志
const winston = require('winston');
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'data-sync' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

// 在非生产环境下，同时输出到控制台
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

## 五、系统监控

### 1. PM2监控

```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 监控性能
pm2 monit
```

### 2. 日志分析

使用ELK栈（Elasticsearch, Logstash, Kibana）或Graylog进行日志收集和分析。

## 六、备份策略

### 1. 数据库备份

```bash
# 创建备份脚本
mkdir -p /backup/mongodb
cat > /backup/backup-mongo.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d-%H%M%S)
BACKUP_DIR="/backup/mongodb"
mongodump --out \$BACKUP_DIR/\$DATE
find \$BACKUP_DIR -type d -mtime +7 -exec rm -rf {} \;
EOF

# 添加可执行权限
chmod +x /backup/backup-mongo.sh

# 添加定时任务
crontab -e
# 添加以下内容（每天凌晨2点执行备份）
0 2 * * * /backup/backup-mongo.sh
```

## 七、故障排除

### 1. 前端常见问题

- 云平台连接失败: 检查token生成函数和参数是否正确
- 数据不更新: 检查定时器是否正常运行

### 2. 后端常见问题

- 数据库连接失败: 检查MongoDB服务是否运行
- API认证失败: 检查token是否有效
- 定时任务未执行: 检查PM2进程状态和日志 